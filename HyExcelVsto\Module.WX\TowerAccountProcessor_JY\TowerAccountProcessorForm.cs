using System;
using System.Windows.Forms;
using ET;
using ET.Controls;
using ET;
using HyExcelVsto.Module.WX.TowerAccountProcessor;

namespace HyExcelVsto.Module.WX.TowerAccountProcessor_JY
{
    /// <summary>
    /// 铁塔内部台账梳理独立窗体 提供铁塔内部台账梳理功能
    /// </summary>
    public partial class TowerAccountProcessorForm : Form
    {
        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        public TowerAccountProcessorForm()
        {
            InitializeComponent();
            InitializeTowerAccountProcessor();

            ETLogManager.Info(this, "TowerAccountProcessorForm初始化完成");
        }

        #endregion 构造函数

        #region 初始化方法

        /// <summary>
        /// 初始化铁塔台账处理器
        /// </summary>
        private void InitializeTowerAccountProcessor()
        {
            try
            {
                // 初始化日志控件
                etLogDisplayControlTower.WriteInfo("铁塔内部台账梳理功能已就绪");
                etLogDisplayControlTower.WriteInfo(TowerAccountProcessorHelper.GetFunctionDescription());

                ETLogManager.Info(this, "铁塔台账处理器初始化完成");
            }
            catch (Exception ex)
            {
                ETLogManager.Error(this, "初始化铁塔台账处理器失败", ex);
            }
        }

        #endregion 初始化方法

        #region 事件处理

        /// <summary>
        /// 铁塔内部台账提取电信部分按钮点击事件
        /// </summary>
        private void BtnExtractTelecomPart_Click(object sender, EventArgs e)
        {
            try
            {
                // 禁用按钮，显示进度
                btnExtractTelecomPart.Enabled = false;
                btnExtractTelecomPart.Text = "提取中...";
                etLogDisplayControlTower.WriteInfo($"[{DateTime.Now:HH:mm:ss}] 开始提取铁塔内部台账电信部分...");
                System.Windows.Forms.Application.DoEvents();

                // 执行提取
                var result = TowerAccountProcessorHelper.ExtractTelecomPart();

                if (result.Success)
                {
                    etLogDisplayControlTower.WriteInfo($"[{DateTime.Now:HH:mm:ss}] ✅ {result.Message}");

                    MessageBox.Show(
                        $"提取完成！\r\n\r\n" +
                        $"新工作表：{result.NewWorksheetName}\r\n" +
                        $"（工作表名称已复制到剪贴板）\r\n\r\n" +
                        "处理内容：\r\n" +
                        "- 删除首行并设置筛选\r\n" +
                        "- 筛选电信相关审核人员数据\r\n" +
                        "- 按会审日期降序、序号升序排序\r\n" +
                        "- 设置冻结窗格",
                        "提取成功",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Information);
                }
                else
                {
                    etLogDisplayControlTower.WriteInfo($"[{DateTime.Now:HH:mm:ss}] ❌ 提取失败：{result.ErrorMessage}");
                    MessageBox.Show($"提取失败：{result.ErrorMessage}", "提取失败", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                etLogDisplayControlTower.WriteInfo($"[{DateTime.Now:HH:mm:ss}] ❌ 提取异常：{ex.Message}");
                MessageBox.Show($"提取时发生异常：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                ETLogManager.Error(this, $"提取铁塔内部台账电信部分异常: {ex.Message}", ex);
            }
            finally
            {
                // 恢复按钮状态
                btnExtractTelecomPart.Enabled = true;
                btnExtractTelecomPart.Text = "铁塔内部台账提取电信部分";
            }
        }

        /// <summary>
        /// 汇总新铁塔台账到铁塔会审台账按钮点击事件
        /// </summary>
        private void BtnSummarizeToAuditAccount_Click(object sender, EventArgs e)
        {
            try
            {
                // 禁用按钮，显示进度
                btnSummarizeToAuditAccount.Enabled = false;
                btnSummarizeToAuditAccount.Text = "汇总中...";
                etLogDisplayControlTower.WriteInfo($"[{DateTime.Now:HH:mm:ss}] 开始汇总新铁塔台账到铁塔会审台账...");
                System.Windows.Forms.Application.DoEvents();

                // 执行汇总
                var result = TowerAccountProcessorHelper.SummarizeToAuditAccount();

                if (result.Success)
                {
                    etLogDisplayControlTower.WriteInfo($"[{DateTime.Now:HH:mm:ss}] ✅ {result.Message}");

                    MessageBox.Show(
                        $"汇总完成！\r\n\r\n" +
                        $"新插入行数：{result.InsertedRows}\r\n\r\n" +
                        "处理内容：\r\n" +
                        "- 根据会审日期去重汇总数据\r\n" +
                        "- 复制公式并转换为数值\r\n" +
                        "- 自动插入新数据到汇总表",
                        "汇总成功",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Information);
                }
                else
                {
                    etLogDisplayControlTower.WriteInfo($"[{DateTime.Now:HH:mm:ss}] ❌ 汇总失败：{result.ErrorMessage}");
                    MessageBox.Show($"汇总失败：{result.ErrorMessage}", "汇总失败", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                etLogDisplayControlTower.WriteInfo($"[{DateTime.Now:HH:mm:ss}] ❌ 汇总异常：{ex.Message}");
                MessageBox.Show($"汇总时发生异常：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                ETLogManager.Error(this, $"汇总新铁塔台账异常: {ex.Message}", ex);
            }
            finally
            {
                // 恢复按钮状态
                btnSummarizeToAuditAccount.Enabled = true;
                btnSummarizeToAuditAccount.Text = "汇总新铁塔台账到铁塔会审台账";
            }
        }

        #endregion 事件处理

        #region 资源释放

        /// <summary>
        /// 释放自定义资源
        /// </summary>
        private void ReleaseCustomResources()
        {
            try
            {
                // 窗体资源清理（如果有需要的话）
                ETLogManager.Info(this, "TowerAccountProcessorForm资源已释放");
            }
            catch (Exception ex)
            {
                ETLogManager.Warning(this, "释放资源时发生异常", ex);
            }
        }

        #endregion 资源释放
    }
}