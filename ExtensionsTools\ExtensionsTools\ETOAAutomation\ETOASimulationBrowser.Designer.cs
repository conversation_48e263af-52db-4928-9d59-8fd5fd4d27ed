namespace ET.ETOAAutomation
{
    partial class ETOASimulationBrowser
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.MenuStrip = new System.Windows.Forms.MenuStrip();
            this.fileToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.MenuFileNew = new System.Windows.Forms.ToolStripMenuItem();
            this.MenuFileOpen = new System.Windows.Forms.ToolStripMenuItem();
            this.MenuFileSeparator1 = new System.Windows.Forms.ToolStripSeparator();
            this.MenuFileExit = new System.Windows.Forms.ToolStripMenuItem();
            this.toolsToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.MenuToolsScreenshot = new System.Windows.Forms.ToolStripMenuItem();
            this.MenuToolsDevTools = new System.Windows.Forms.ToolStripMenuItem();
            this.PanelControls = new System.Windows.Forms.Panel();
            this.BtnRefresh = new System.Windows.Forms.Button();
            this.BtnForward = new System.Windows.Forms.Button();
            this.BtnBack = new System.Windows.Forms.Button();
            this.BtnNavigate = new System.Windows.Forms.Button();
            this.TxtUrl = new System.Windows.Forms.TextBox();
            this.label1 = new System.Windows.Forms.Label();
            this.panelBrowser = new System.Windows.Forms.Panel();
            this.panelStatus = new System.Windows.Forms.Panel();
            this.ProgressBar = new System.Windows.Forms.ProgressBar();
            this.LblStatus = new System.Windows.Forms.Label();
            this.MenuStrip.SuspendLayout();
            this.PanelControls.SuspendLayout();
            this.panelStatus.SuspendLayout();
            this.SuspendLayout();
            //
            // MenuStrip
            //
            this.MenuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.fileToolStripMenuItem,
            this.toolsToolStripMenuItem});
            this.MenuStrip.Location = new System.Drawing.Point(0, 0);
            this.MenuStrip.Name = "MenuStrip";
            this.MenuStrip.Size = new System.Drawing.Size(1000, 25);
            this.MenuStrip.TabIndex = 0;
            this.MenuStrip.Text = "menuStrip1";
            //
            // fileToolStripMenuItem
            //
            this.fileToolStripMenuItem.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.MenuFileNew,
            this.MenuFileOpen,
            this.MenuFileSeparator1,
            this.MenuFileExit});
            this.fileToolStripMenuItem.Name = "fileToolStripMenuItem";
            this.fileToolStripMenuItem.Size = new System.Drawing.Size(44, 21);
            this.fileToolStripMenuItem.Text = "文件";
            //
            // MenuFileNew
            //
            this.MenuFileNew.Name = "MenuFileNew";
            this.MenuFileNew.Size = new System.Drawing.Size(124, 22);
            this.MenuFileNew.Text = "新建窗口";
            this.MenuFileNew.Click += new System.EventHandler(this.MenuFileNew_Click);
            //
            // MenuFileOpen
            //
            this.MenuFileOpen.Name = "MenuFileOpen";
            this.MenuFileOpen.Size = new System.Drawing.Size(124, 22);
            this.MenuFileOpen.Text = "打开文件";
            this.MenuFileOpen.Click += new System.EventHandler(this.MenuFileOpen_Click);
            //
            // MenuFileSeparator1
            //
            this.MenuFileSeparator1.Name = "MenuFileSeparator1";
            this.MenuFileSeparator1.Size = new System.Drawing.Size(121, 6);
            //
            // MenuFileExit
            //
            this.MenuFileExit.Name = "MenuFileExit";
            this.MenuFileExit.Size = new System.Drawing.Size(124, 22);
            this.MenuFileExit.Text = "退出";
            this.MenuFileExit.Click += new System.EventHandler(this.MenuFileExit_Click);
            //
            // toolsToolStripMenuItem
            //
            this.toolsToolStripMenuItem.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.MenuToolsScreenshot,
            this.MenuToolsDevTools});
            this.toolsToolStripMenuItem.Name = "toolsToolStripMenuItem";
            this.toolsToolStripMenuItem.Size = new System.Drawing.Size(44, 21);
            this.toolsToolStripMenuItem.Text = "工具";
            //
            // MenuToolsScreenshot
            //
            this.MenuToolsScreenshot.Name = "MenuToolsScreenshot";
            this.MenuToolsScreenshot.Size = new System.Drawing.Size(136, 22);
            this.MenuToolsScreenshot.Text = "截图";
            this.MenuToolsScreenshot.Click += new System.EventHandler(this.MenuToolsScreenshot_Click);
            //
            // MenuToolsDevTools
            //
            this.MenuToolsDevTools.Name = "MenuToolsDevTools";
            this.MenuToolsDevTools.Size = new System.Drawing.Size(136, 22);
            this.MenuToolsDevTools.Text = "开发者工具";
            this.MenuToolsDevTools.Click += new System.EventHandler(this.MenuToolsDevTools_Click);
            // 
            // PanelControls
            // 
            this.PanelControls.Controls.Add(this.BtnRefresh);
            this.PanelControls.Controls.Add(this.BtnForward);
            this.PanelControls.Controls.Add(this.BtnBack);
            this.PanelControls.Controls.Add(this.BtnNavigate);
            this.PanelControls.Controls.Add(this.TxtUrl);
            this.PanelControls.Controls.Add(this.label1);
            this.PanelControls.Dock = System.Windows.Forms.DockStyle.Top;
            this.PanelControls.Location = new System.Drawing.Point(0, 25);
            this.PanelControls.Name = "PanelControls";
            this.PanelControls.Size = new System.Drawing.Size(1000, 50);
            this.PanelControls.TabIndex = 1;
            // 
            // BtnRefresh
            // 
            this.BtnRefresh.Location = new System.Drawing.Point(180, 15);
            this.BtnRefresh.Name = "BtnRefresh";
            this.BtnRefresh.Size = new System.Drawing.Size(50, 23);
            this.BtnRefresh.TabIndex = 5;
            this.BtnRefresh.Text = "刷新";
            this.BtnRefresh.UseVisualStyleBackColor = true;
            this.BtnRefresh.Click += new System.EventHandler(this.BtnRefresh_Click);
            // 
            // BtnForward
            // 
            this.BtnForward.Location = new System.Drawing.Point(130, 15);
            this.BtnForward.Name = "BtnForward";
            this.BtnForward.Size = new System.Drawing.Size(50, 23);
            this.BtnForward.TabIndex = 4;
            this.BtnForward.Text = "前进";
            this.BtnForward.UseVisualStyleBackColor = true;
            this.BtnForward.Click += new System.EventHandler(this.BtnForward_Click);
            // 
            // BtnBack
            // 
            this.BtnBack.Location = new System.Drawing.Point(80, 15);
            this.BtnBack.Name = "BtnBack";
            this.BtnBack.Size = new System.Drawing.Size(50, 23);
            this.BtnBack.TabIndex = 3;
            this.BtnBack.Text = "后退";
            this.BtnBack.UseVisualStyleBackColor = true;
            this.BtnBack.Click += new System.EventHandler(this.BtnBack_Click);
            // 
            // BtnNavigate
            // 
            this.BtnNavigate.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.BtnNavigate.Location = new System.Drawing.Point(920, 15);
            this.BtnNavigate.Name = "BtnNavigate";
            this.BtnNavigate.Size = new System.Drawing.Size(75, 23);
            this.BtnNavigate.TabIndex = 2;
            this.BtnNavigate.Text = "转到";
            this.BtnNavigate.UseVisualStyleBackColor = true;
            this.BtnNavigate.Click += new System.EventHandler(this.BtnNavigate_Click);
            // 
            // TxtUrl
            // 
            this.TxtUrl.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.TxtUrl.Location = new System.Drawing.Point(280, 17);
            this.TxtUrl.Name = "TxtUrl";
            this.TxtUrl.Size = new System.Drawing.Size(634, 21);
            this.TxtUrl.TabIndex = 1;
            this.TxtUrl.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.TxtUrl_KeyPress);
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(240, 20);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(35, 12);
            this.label1.TabIndex = 0;
            this.label1.Text = "地址:";
            // 
            // panelBrowser
            // 
            this.panelBrowser.Dock = System.Windows.Forms.DockStyle.Fill;
            this.panelBrowser.Location = new System.Drawing.Point(0, 75);
            this.panelBrowser.Name = "panelBrowser";
            this.panelBrowser.Size = new System.Drawing.Size(1000, 525);
            this.panelBrowser.TabIndex = 2;
            // 
            // panelStatus
            // 
            this.panelStatus.Controls.Add(this.ProgressBar);
            this.panelStatus.Controls.Add(this.LblStatus);
            this.panelStatus.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.panelStatus.Location = new System.Drawing.Point(0, 600);
            this.panelStatus.Name = "panelStatus";
            this.panelStatus.Size = new System.Drawing.Size(1000, 50);
            this.panelStatus.TabIndex = 3;
            // 
            // ProgressBar
            // 
            this.ProgressBar.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.ProgressBar.Location = new System.Drawing.Point(12, 25);
            this.ProgressBar.Name = "ProgressBar";
            this.ProgressBar.Size = new System.Drawing.Size(976, 15);
            this.ProgressBar.TabIndex = 1;
            // 
            // LblStatus
            // 
            this.LblStatus.AutoSize = true;
            this.LblStatus.Location = new System.Drawing.Point(12, 10);
            this.LblStatus.Name = "LblStatus";
            this.LblStatus.Size = new System.Drawing.Size(29, 12);
            this.LblStatus.TabIndex = 0;
            this.LblStatus.Text = "就绪";
            // 
            // ETOASimulationBrowser
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1000, 650);
            this.Controls.Add(this.panelBrowser);
            this.Controls.Add(this.panelStatus);
            this.Controls.Add(this.PanelControls);
            this.Controls.Add(this.MenuStrip);
            this.MainMenuStrip = this.MenuStrip;
            this.Name = "ETOASimulationBrowser";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "模拟操作浏览器";
            this.WindowState = System.Windows.Forms.FormWindowState.Maximized;
            this.MenuStrip.ResumeLayout(false);
            this.MenuStrip.PerformLayout();
            this.PanelControls.ResumeLayout(false);
            this.PanelControls.PerformLayout();
            this.panelStatus.ResumeLayout(false);
            this.panelStatus.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.ToolStripMenuItem fileToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem MenuFileNew;
        private System.Windows.Forms.ToolStripMenuItem MenuFileOpen;
        private System.Windows.Forms.ToolStripSeparator MenuFileSeparator1;
        private System.Windows.Forms.ToolStripMenuItem MenuFileExit;
        private System.Windows.Forms.ToolStripMenuItem toolsToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem MenuToolsScreenshot;
        private System.Windows.Forms.ToolStripMenuItem MenuToolsDevTools;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Panel panelBrowser;
        private System.Windows.Forms.Panel panelStatus;

        /// <summary>
        /// 导航按钮点击事件
        /// </summary>
        private void BtnNavigate_Click(object sender, System.EventArgs e)
        {
            if (!string.IsNullOrEmpty(TxtUrl.Text))
            {
                _ = NavigateAsync(TxtUrl.Text);
            }
        }

        /// <summary>
        /// 后退按钮点击事件
        /// </summary>
        private void BtnBack_Click(object sender, System.EventArgs e)
        {
            if (_browser?.CanGoBack == true)
            {
                _browser.Back();
            }
        }

        /// <summary>
        /// 前进按钮点击事件
        /// </summary>
        private void BtnForward_Click(object sender, System.EventArgs e)
        {
            if (_browser?.CanGoForward == true)
            {
                _browser.Forward();
            }
        }

        /// <summary>
        /// 刷新按钮点击事件
        /// </summary>
        private void BtnRefresh_Click(object sender, System.EventArgs e)
        {
            _browser?.Reload();
        }

        /// <summary>
        /// URL输入框按键事件
        /// </summary>
        private void TxtUrl_KeyPress(object sender, System.Windows.Forms.KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)13) // Enter键
            {
                BtnNavigate_Click(sender, e);
            }
        }

        /// <summary>
        /// 新建窗口菜单点击事件
        /// </summary>
        private void MenuFileNew_Click(object sender, System.EventArgs e)
        {
            var newBrowser = new ETOASimulationBrowser();
            newBrowser.Show();
        }

        /// <summary>
        /// 打开文件菜单点击事件
        /// </summary>
        private void MenuFileOpen_Click(object sender, System.EventArgs e)
        {
            using (var openFileDialog = new System.Windows.Forms.OpenFileDialog())
            {
                openFileDialog.Filter = "HTML文件|*.html;*.htm|所有文件|*.*";
                if (openFileDialog.ShowDialog() == System.Windows.Forms.DialogResult.OK)
                {
                    _ = NavigateAsync($"file:///{openFileDialog.FileName}");
                }
            }
        }

        /// <summary>
        /// 退出菜单点击事件
        /// </summary>
        private void MenuFileExit_Click(object sender, System.EventArgs e)
        {
            this.Close();
        }

        /// <summary>
        /// 截图菜单点击事件
        /// </summary>
        private void MenuToolsScreenshot_Click(object sender, System.EventArgs e)
        {
            using (var saveFileDialog = new System.Windows.Forms.SaveFileDialog())
            {
                saveFileDialog.Filter = "PNG图片|*.png|JPEG图片|*.jpg|所有文件|*.*";
                saveFileDialog.DefaultExt = "png";
                if (saveFileDialog.ShowDialog() == System.Windows.Forms.DialogResult.OK)
                {
                    _ = SaveScreenshotAsync(saveFileDialog.FileName);
                }
            }
        }

        /// <summary>
        /// 开发者工具菜单点击事件
        /// </summary>
        private void MenuToolsDevTools_Click(object sender, System.EventArgs e)
        {
            _browser?.ShowDevTools();
        }
    }
}
