using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using Microsoft.Office.Interop.Excel;
using HyExcelVsto.Module.Common.StationDataProcessor.Core;
using HyExcelVsto.Module.Common.StationDataProcessor.Core.Interfaces;
using ET;
using ET;

// 类型别名，简化长类型名称
using CoreProgressEventArgs = HyExcelVsto.Module.Common.StationDataProcessor.Core.Interfaces.ProgressEventArgs;
using ETLogDisplayControl = ET.Controls.ETLogDisplayControl;

namespace HyExcelVsto.Module.Common.StationDataProcessor
{
    /// <summary>
    /// 基站数据处理器助手类 提供基站数据处理的核心业务逻辑，与UI分离
    /// </summary>
    public static class StationDataProcessorHelper
    {
        #region 配置文件管理

        /// <summary>
        /// 获取配置文件目录路径
        /// </summary>
        /// <returns>配置文件目录路径</returns>
        public static string GetConfigDirectory()
        {
            try
            {
                string configDir = ETConfig.GetConfigDirectory(".stationdata");
                if (!Directory.Exists(configDir))
                {
                    Directory.CreateDirectory(configDir);
                    ETLogManager.Info($"创建配置目录: {configDir}");
                }
                return configDir;
            }
            catch (Exception ex)
            {
                ETLogManager.Error(ex);
                throw new ETException("获取配置目录失败", "配置管理", ex);
            }
        }

        /// <summary>
        /// 获取所有可用的配置文件
        /// </summary>
        /// <returns>配置文件名列表</returns>
        public static List<string> GetAvailableConfigFiles()
        {
            try
            {
                string configDir = GetConfigDirectory();
                var configFiles = Directory.GetFiles(configDir, "*.config")
                    .Select(Path.GetFileNameWithoutExtension)
                    .ToList();

                ETLogManager.Info($"找到 {configFiles.Count} 个配置文件");
                return configFiles;
            }
            catch (Exception ex)
            {
                ETLogManager.Error(ex);
                return new List<string>();
            }
        }

        /// <summary>
        /// 打开配置文件目录
        /// </summary>
        public static void OpenConfigDirectory()
        {
            try
            {
                string configDir = GetConfigDirectory();
                System.Diagnostics.Process.Start("explorer.exe", configDir);
                ETLogManager.Info($"打开配置目录: {configDir}");
            }
            catch (Exception ex)
            {
                ETLogManager.Error(ex);
                throw new ETException("打开配置目录失败", "配置管理", ex);
            }
        }

        /// <summary>
        /// 初始化配置文件
        /// </summary>
        /// <param name="selectedConfigFile">选中的配置文件名（不含扩展名）</param>
        /// <param name="logWriter">日志写入委托</param>
        /// <returns>初始化是否成功</returns>
        public static async Task<bool> InitializeConfigurationAsync(string selectedConfigFile, System.Action<string> logWriter)
        {
            try
            {
                // 构建配置文件完整路径
                string configDir = GetConfigDirectory();
                string configFilePath = Path.Combine(configDir, $"{selectedConfigFile}.config");

                logWriter?.Invoke($"📋 正在初始化配置文件：{selectedConfigFile}");
                ETLogManager.Info($"开始初始化配置文件：{configFilePath}");

                // 检查配置文件是否存在
                if (!File.Exists(configFilePath))
                {
                    string errorMsg = $"配置文件不存在：{configFilePath}";
                    logWriter?.Invoke($"❌ {errorMsg}");
                    ETLogManager.Error(errorMsg);
                    return false;
                }

                // 调用配置类的初始化方法
                await Task.Run(() => StationDataProcessorConfig.Initialize(configFilePath));

                logWriter?.Invoke($"✅ 配置文件初始化成功：{selectedConfigFile}");
                ETLogManager.Info($"配置文件初始化成功：{configFilePath}");
                return true;
            }
            catch (Exception ex)
            {
                string errorMsg = $"配置文件初始化失败：{ex.Message}";
                logWriter?.Invoke($"❌ {errorMsg}");
                ETLogManager.Error(ex);
                return false;
            }
        }

        #endregion 配置文件管理

        #region 处理选项摘要

        /// <summary>
        /// 显示处理选项摘要
        /// </summary>
        /// <param name="logWriter">日志写入委托</param>
        public static void ShowProcessingOptionsSummary(System.Action<string> logWriter)
        {
            try
            {
                logWriter?.Invoke("=== 处理选项摘要 ===");
                logWriter?.Invoke("• 隐藏指定列: 是");
                logWriter?.Invoke("• 按扇区中文名排序: 是");
                logWriter?.Invoke("• 添加分析列: 是");
                logWriter?.Invoke("• 设置自动筛选: 是");
                logWriter?.Invoke("========================");
            }
            catch (Exception ex)
            {
                ETLogManager.Error(ex);
                logWriter?.Invoke($"显示处理选项摘要时发生错误: {ex.Message}");
            }
        }

        #endregion 处理选项摘要

        #region 处理确认

        /// <summary>
        /// 确认处理操作
        /// </summary>
        /// <param name="selectedRange">选中的数据范围</param>
        /// <param name="parentForm">父窗体</param>
        /// <returns>用户是否确认</returns>
        public static bool ConfirmProcessingOperation(Range selectedRange, Form parentForm = null)
        {
            try
            {
                var rowCount = selectedRange?.Rows.Count ?? 0;

                string message = $"即将处理 {rowCount} 行基站数据，包括以下操作：\n\n";

                // 固定执行所有处理操作，不再根据复选框状态判断
                message += "• 隐藏指定列\n";
                message += "• 按扇区中文名排序\n";
                message += "• 添加12个分析列\n";
                message += "• 设置自动筛选\n";

                message += "\n确定要继续吗？";

                var result = MessageBox.Show(parentForm, message, "确认处理",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                return result == DialogResult.Yes;
            }
            catch (Exception ex)
            {
                ETLogManager.Error(ex);
                MessageBox.Show(parentForm, $"确认处理操作时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }

        #endregion 处理确认

        #region 核心处理执行

        /// <summary>
        /// 执行基站数据处理
        /// </summary>
        /// <param name="worksheet">工作表</param>
        /// <param name="selectedRange">选中的数据范围</param>
        /// <param name="selectedConfigFile">选中的配置文件名</param>
        /// <param name="logWriter">日志写入委托</param>
        /// <param name="progressReporter">进度报告委托</param>
        /// <returns>处理结果</returns>
        public static async Task<ProcessResult> ExecuteProcessingAsync(
            Worksheet worksheet,
            Range selectedRange,
            string selectedConfigFile,
            System.Action<string> logWriter,
            IProgress<ProgressEventArgs> progressReporter = null)
        {
            Core.StationDataProcessor processor = null;
            IExcelDataAccess dataAccess = null;
            IDataAnalyzer analyzer = null;

            try
            {
                // 1. 初始化配置文件
                if (!await InitializeConfigurationAsync(selectedConfigFile, logWriter))
                {
                    return new ProcessResult { Success = false, Message = "配置文件初始化失败" };
                }

                // 2. 显示处理选项摘要
                ShowProcessingOptionsSummary(logWriter);

                // 3. 创建数据访问层（会进行基本验证）
                dataAccess = new ExcelDataAccess(worksheet, selectedRange, logWriter);

                // 4. 创建数据分析器
                analyzer = new StationDataAnalyzer();

                // 5. 创建处理器
                processor = new Core.StationDataProcessor(dataAccess, analyzer, logWriter);

                // 6. 订阅进度事件
                if (progressReporter != null)
                {
                    processor.ProgressChanged += (sender, args) => progressReporter.Report(args);
                }

                // 7. 记录开始时间
                var startTime = DateTime.Now;
                logWriter?.Invoke($"⏰ 开始时间：{startTime:yyyy-MM-dd HH:mm:ss}");

                // 8. 执行处理（包含完整的数据验证）
                var result = await processor.ProcessStationDataAsync(worksheet);

                // 9. 记录结束时间和耗时
                var endTime = DateTime.Now;
                var duration = endTime - startTime;
                logWriter?.Invoke($"⏰ 结束时间：{endTime:yyyy-MM-dd HH:mm:ss}");
                logWriter?.Invoke($"⏱️ 总耗时：{duration.TotalSeconds:F2} 秒");

                // 10. 处理结果
                if (result.Success)
                {
                    logWriter?.Invoke("🎉 基站数据处理完成！");
                    logWriter?.Invoke($"✅ 处理结果：{result.Message}");
                }
                else
                {
                    logWriter?.Invoke("❌ 基站数据处理失败");
                    logWriter?.Invoke($"❌ 错误信息：{result.Message}");
                }

                return result;
            }
            catch (Exception ex)
            {
                ETLogManager.Error(ex);
                var errorMessage = $"处理过程中发生异常: {ex.Message}";
                logWriter?.Invoke($"❌ {errorMessage}");
                return new ProcessResult { Success = false, Message = errorMessage };
            }
            finally
            {
                // 清理资源
                processor?.Dispose();
                if (dataAccess is IDisposable disposableDataAccess)
                {
                    disposableDataAccess.Dispose();
                }
                if (analyzer is IDisposable disposableAnalyzer)
                {
                    disposableAnalyzer.Dispose();
                }
            }
        }

        /// <summary>
        /// 取消正在进行的处理
        /// </summary>
        /// <param name="processor">处理器实例</param>
        public static void CancelProcessing(Core.StationDataProcessor processor)
        {
            try
            {
                processor?.CancelProcessing();
                ETLogManager.Info("已请求取消处理操作");
            }
            catch (Exception ex)
            {
                ETLogManager.Error(ex);
            }
        }

        #endregion 核心处理执行

        #region UI辅助功能

        /// <summary>
        /// 处理结果的UI更新信息
        /// </summary>
        public class ProcessingUIInfo
        {
            public bool Success { get; set; }
            public string StatusText { get; set; }
            public string LogMessage { get; set; }
            public string UserMessage { get; set; }
            public string UserTitle { get; set; }
            public MessageBoxIcon UserIcon { get; set; }
            public bool PlaySound { get; set; }
            public System.Media.SystemSound SoundToPlay { get; set; }
            public int ProgressValue { get; set; } = 100;
            public string ProgressText { get; set; } = "100%";
        }

        /// <summary>
        /// 生成处理结果的UI更新信息
        /// </summary>
        /// <param name="result">处理结果</param>
        /// <returns>UI更新信息</returns>
        public static ProcessingUIInfo GenerateProcessingUIInfo(ProcessResult result)
        {
            var uiInfo = new ProcessingUIInfo();

            if (result.Success)
            {
                uiInfo.Success = true;
                uiInfo.StatusText = $"✅ 处理完成！共处理 {result.ProcessedRows} 行数据";
                uiInfo.LogMessage = $"🎉 数据处理完成，共处理 {result.ProcessedRows} 行数据";
                uiInfo.UserMessage = $"数据处理完成！\n\n共处理 {result.ProcessedRows} 行数据\n\n{result.Message}";
                uiInfo.UserTitle = "处理成功";
                uiInfo.UserIcon = MessageBoxIcon.Information;
                uiInfo.PlaySound = true;
                uiInfo.SoundToPlay = System.Media.SystemSounds.Asterisk;
            }
            else
            {
                uiInfo.Success = false;
                uiInfo.StatusText = "❌ 处理失败";
                uiInfo.LogMessage = $"处理失败：{result.Message}";
                uiInfo.UserMessage = $"处理失败：{result.Message}\n\n请检查日志获取详细信息";
                uiInfo.UserTitle = "处理失败";
                uiInfo.UserIcon = MessageBoxIcon.Error;
                uiInfo.PlaySound = true;
                uiInfo.SoundToPlay = System.Media.SystemSounds.Hand;
            }

            return uiInfo;
        }

        /// <summary>
        /// 生成处理异常的UI更新信息
        /// </summary>
        /// <param name="ex">异常</param>
        /// <returns>UI更新信息</returns>
        public static ProcessingUIInfo GenerateExceptionUIInfo(Exception ex)
        {
            return new ProcessingUIInfo
            {
                Success = false,
                StatusText = "⚠️ 处理异常",
                LogMessage = "处理过程中发生异常",
                UserMessage = $"处理过程中发生异常：\n\n{ex.Message}\n\n请检查日志获取详细信息",
                UserTitle = "处理异常",
                UserIcon = MessageBoxIcon.Error,
                PlaySound = true,
                SoundToPlay = System.Media.SystemSounds.Exclamation
            };
        }

        #endregion UI辅助功能

        #region 简化的窗体辅助方法

        /// <summary>
        /// 创建线程安全的日志写入委托
        /// </summary>
        /// <param name="logControl">日志显示控件</param>
        /// <param name="form">窗体实例</param>
        /// <returns>线程安全的日志写入委托</returns>
        public static System.Action<string> CreateThreadSafeLogWriter(ETLogDisplayControl logControl, Form form)
        {
            return (message) =>
            {
                try
                {
                    if (form.InvokeRequired)
                    {
                        form.Invoke(new System.Action(() => logControl.WriteInfo(message)));
                    }
                    else
                    {
                        logControl.WriteInfo(message);
                    }
                }
                catch
                {
                    // 忽略UI线程调用异常
                }
            };
        }

        /// <summary>
        /// 创建线程安全的消息显示委托
        /// </summary>
        /// <param name="form">窗体实例</param>
        /// <returns>线程安全的消息显示委托</returns>
        public static System.Action<string, string, MessageBoxIcon> CreateThreadSafeMessageHandler(Form form)
        {
            return (message, title, icon) =>
            {
                try
                {
                    if (form.InvokeRequired)
                    {
                        form.Invoke(new System.Action(() => MessageBox.Show(form, message, title, MessageBoxButtons.OK, icon)));
                    }
                    else
                    {
                        MessageBox.Show(form, message, title, MessageBoxButtons.OK, icon);
                    }
                }
                catch
                {
                    // 忽略UI线程调用异常
                }
            };
        }

        /// <summary>
        /// 验证工作表和数据范围的基本有效性
        /// </summary>
        /// <param name="worksheet">工作表</param>
        /// <param name="messageHandler">消息处理委托</param>
        /// <returns>验证是否通过</returns>
        public static bool ValidateBasicWorksheetAndRange(Worksheet worksheet, System.Action<string, string, MessageBoxIcon> messageHandler)
        {
            if (worksheet == null)
            {
                messageHandler?.Invoke("请先选择要处理的工作表和数据范围", "选择数据范围", MessageBoxIcon.Warning);
                return false;
            }
            return true;
        }

        #endregion 简化的窗体辅助方法

        #region 进度处理辅助

        /// <summary>
        /// 进度更新信息
        /// </summary>
        public class ProgressUpdateInfo
        {
            public int ProgressPercentage { get; set; }
            public string ProgressText { get; set; }
            public string CurrentStep { get; set; }
            public string StatusText { get; set; }
            public bool IsImportantStep { get; set; }
        }

        /// <summary>
        /// 生成进度更新信息
        /// </summary>
        /// <param name="e">进度事件参数</param>
        /// <returns>进度更新信息</returns>
        public static ProgressUpdateInfo GenerateProgressUpdateInfo(CoreProgressEventArgs e)
        {
            var info = new ProgressUpdateInfo
            {
                ProgressPercentage = Math.Min(e.ProgressPercentage, 100),
                ProgressText = $"{e.ProgressPercentage}%",
                CurrentStep = e.CurrentStep,
                StatusText = $"🔄 {e.CurrentStep}",
                IsImportantStep = IsImportantStep(e.CurrentStep)
            };

            return info;
        }

        /// <summary>
        /// 判断是否为重要步骤
        /// </summary>
        /// <param name="stepName">步骤名称</param>
        /// <returns>是否为重要步骤</returns>
        private static bool IsImportantStep(string stepName)
        {
            if (string.IsNullOrEmpty(stepName)) return false;

            var lowerStep = stepName.ToLower();
            return lowerStep.Contains("开始") ||
                   lowerStep.Contains("完成") ||
                   lowerStep.Contains("验证") ||
                   lowerStep.Contains("处理") ||
                   lowerStep.Contains("分析") ||
                   lowerStep.Contains("排序") ||
                   lowerStep.Contains("筛选");
        }

        /// <summary>
        /// 创建线程安全的进度更新委托
        /// </summary>
        /// <param name="form">窗体实例</param>
        /// <param name="progressBar">进度条控件</param>
        /// <param name="lblProgress">进度标签</param>
        /// <param name="lblCurrentStep">当前步骤标签</param>
        /// <param name="lblStatus">状态标签</param>
        /// <param name="logControl">日志控件</param>
        /// <returns>线程安全的进度更新委托</returns>
        public static System.Action<CoreProgressEventArgs> CreateThreadSafeProgressHandler(
            Form form,
            System.Windows.Forms.ProgressBar progressBar,
            System.Windows.Forms.Label lblProgress,
            System.Windows.Forms.Label lblCurrentStep,
            System.Windows.Forms.Label lblStatus,
            ETLogDisplayControl logControl)
        {
            return (e) =>
            {
                try
                {
                    if (form.InvokeRequired)
                    {
                        form.Invoke(new System.Action(() =>
                        {
                            var info = GenerateProgressUpdateInfo(e);
                            UpdateProgressControls(progressBar, lblProgress, lblCurrentStep, lblStatus, logControl, info);
                        }));
                    }
                    else
                    {
                        var info = GenerateProgressUpdateInfo(e);
                        UpdateProgressControls(progressBar, lblProgress, lblCurrentStep, lblStatus, logControl, info);
                    }
                }
                catch
                {
                    // 忽略UI线程调用异常
                }
            };
        }

        /// <summary>
        /// 更新进度控件
        /// </summary>
        private static void UpdateProgressControls(
            System.Windows.Forms.ProgressBar progressBar,
            System.Windows.Forms.Label lblProgress,
            System.Windows.Forms.Label lblCurrentStep,
            System.Windows.Forms.Label lblStatus,
            ETLogDisplayControl logControl,
            ProgressUpdateInfo info)
        {
            // 更新进度条
            progressBar.Style = ProgressBarStyle.Blocks;
            progressBar.Value = info.ProgressPercentage;

            // 更新标签
            lblProgress.Text = info.ProgressText;
            lblCurrentStep.Text = info.CurrentStep;
            lblStatus.Text = info.StatusText;

            // 记录重要步骤到日志
            if (info.IsImportantStep)
            {
                logControl.WriteInfo($"📍 {info.CurrentStep}");
            }
        }

        #endregion 进度处理辅助
    }
}