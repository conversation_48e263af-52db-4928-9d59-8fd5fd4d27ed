using System;
using System.Windows.Forms;
using Microsoft.Office.Interop.Excel;
using ET;
using HyExcelVsto.Extensions;
using ET;

namespace HyExcelVsto.Module.WX.AngleExtractor
{
    /// <summary>
    /// 方向角和下倾角提取器帮助类 提供从Excel数据中提取方向角和下倾角的功能
    /// </summary>
    public static class AngleExtractorHelper
    {
        /// <summary>
        /// 提取方向角和下倾角的结果
        /// </summary>
        public class ExtractionResult
        {
            /// <summary>
            /// 是否成功
            /// </summary>
            public bool Success { get; set; }

            /// <summary>
            /// 处理的行数
            /// </summary>
            public int ProcessedRows { get; set; }

            /// <summary>
            /// 提取的方向角数量
            /// </summary>
            public int AzimuthCount { get; set; }

            /// <summary>
            /// 提取的下倾角数量
            /// </summary>
            public int TiltAngleCount { get; set; }

            /// <summary>
            /// 错误消息
            /// </summary>
            public string ErrorMessage { get; set; }

            /// <summary>
            /// 详细消息
            /// </summary>
            public string Message { get; set; }
        }

        /// <summary>
        /// 提取方向角和下倾角的参数
        /// </summary>
        public class ExtractionParameters
        {
            /// <summary>
            /// 源数据范围
            /// </summary>
            public Range SourceRange { get; set; }

            /// <summary>
            /// 方向角输出范围
            /// </summary>
            public Range AzimuthOutputRange { get; set; }

            /// <summary>
            /// 下倾角输出范围
            /// </summary>
            public Range TiltAngleOutputRange { get; set; }

            /// <summary>
            /// 是否跳过筛选行
            /// </summary>
            public bool SkipFilterRow { get; set; } = true;

            /// <summary>
            /// 最大处理行数限制
            /// </summary>
            public int MaxRowsLimit { get; set; } = 30000;
        }

        /// <summary>
        /// 验证提取参数
        /// </summary>
        /// <param name="parameters">提取参数</param>
        /// <returns>验证结果</returns>
        public static ExtractionResult ValidateParameters(ExtractionParameters parameters)
        {
            try
            {
                if (parameters == null)
                {
                    return new ExtractionResult
                    {
                        Success = false,
                        ErrorMessage = "提取参数不能为空"
                    };
                }

                if (parameters.SourceRange == null)
                {
                    return new ExtractionResult
                    {
                        Success = false,
                        ErrorMessage = "请选择源数据范围"
                    };
                }

                if (parameters.AzimuthOutputRange == null && parameters.TiltAngleOutputRange == null)
                {
                    return new ExtractionResult
                    {
                        Success = false,
                        ErrorMessage = "请至少选择一个输出范围（方向角或下倾角）"
                    };
                }

                if (parameters.SourceRange.Cells.Count > parameters.MaxRowsLimit)
                {
                    return new ExtractionResult
                    {
                        Success = false,
                        ErrorMessage = $"源数据范围过大，最多支持 {parameters.MaxRowsLimit} 个单元格"
                    };
                }

                return new ExtractionResult { Success = true };
            }
            catch (Exception ex)
            {
                ETLogManager.Error("验证提取参数失败", ex);
                return new ExtractionResult
                {
                    Success = false,
                    ErrorMessage = $"验证参数时发生错误：{ex.Message}"
                };
            }
        }

        /// <summary>
        /// 执行方向角和下倾角提取
        /// </summary>
        /// <param name="parameters">提取参数</param>
        /// <returns>提取结果</returns>
        public static ExtractionResult ExtractAngles(ExtractionParameters parameters)
        {
            try
            {
                ETLogManager.Info("开始提取方向角和下倾角");

                // 智能范围分配：如果AzimuthOutputRange包含2列且TiltAngleOutputRange为null，则自动分配
                if (parameters.AzimuthOutputRange != null &&
                    parameters.TiltAngleOutputRange == null &&
                    parameters.AzimuthOutputRange.Columns.Count == 2)
                {
                    ETLogManager.Info("检测到方向角输出范围包含2列且下倾角输出范围为空，自动分配列范围");

                    // 保存原始范围引用
                    Range originalRange = parameters.AzimuthOutputRange;

                    // 第一列作为方向角输出范围
                    Range firstColumn = originalRange.Columns[1];
                    parameters.AzimuthOutputRange = firstColumn;

                    // 第二列作为下倾角输出范围
                    Range secondColumn = originalRange.Columns[2];
                    parameters.TiltAngleOutputRange = secondColumn;

                    ETLogManager.Info($"自动分配完成 - 方向角输出列：{firstColumn.Address}，下倾角输出列：{secondColumn.Address}");
                }

                // 验证参数
                var validationResult = ValidateParameters(parameters);
                if (!validationResult.Success)
                {
                    return validationResult;
                }

                // 获取筛选行号
                int filterRow = 0;
                if (parameters.SkipFilterRow)
                {
                    filterRow = ETExcelExtensions.GetAutoFilterRowNumber();
                }

                // 优化源数据范围
                Range inputRange = parameters.SourceRange.OptimizeRangeSize().GetVisibleRange();
                Range outputRange1 = parameters.AzimuthOutputRange?.Cells[1, 1].EntireColumn;
                Range outputRange2 = parameters.TiltAngleOutputRange?.Cells[1, 1].EntireColumn;

                // 设置Excel快速模式
                ETExcelExtensions.SetAppFastMode();

                int processedRows = 0;
                int azimuthCount = 0;
                int tiltAngleCount = 0;

                try
                {
                    foreach (Range cell in inputRange.Cells)
                    {
                        // 跳过筛选行
                        if (parameters.SkipFilterRow && cell.Row <= filterRow)
                            continue;

                        processedRows++;

                        // 提取并输出方向角
                        if (outputRange1 != null)
                        {
                            Range outputCell1 = inputRange.Worksheet.Cells[cell.Row, outputRange1.Columns[1].Column];
                            outputCell1.Clear清除内容();
                            outputCell1.Set文本格式();

                            string azimuths = ZnWireless.ExtractAzimuths(cell?.Value?.ToString());
                            if (!string.IsNullOrEmpty(azimuths))
                            {
                                outputCell1.Value = azimuths;
                                azimuthCount++;
                            }
                        }

                        // 提取并输出下倾角
                        if (outputRange2 != null)
                        {
                            Range outputCell2 = inputRange.Worksheet.Cells[cell.Row, outputRange2.Columns[1].Column];
                            outputCell2.Clear清除内容();
                            outputCell2.Set文本格式();

                            string tiltAngles = ZnWireless.ExtractDowntilts(cell?.Value?.ToString());
                            if (!string.IsNullOrEmpty(tiltAngles))
                            {
                                outputCell2.Value = tiltAngles;
                                tiltAngleCount++;
                            }
                        }
                    }

                    string message = $"提取完成！处理了 {processedRows} 行数据，" +
                                   $"提取到 {azimuthCount} 个方向角，{tiltAngleCount} 个下倾角";

                    ETLogManager.Info(message);

                    return new ExtractionResult
                    {
                        Success = true,
                        ProcessedRows = processedRows,
                        AzimuthCount = azimuthCount,
                        TiltAngleCount = tiltAngleCount,
                        Message = message
                    };
                }
                finally
                {
                    // 恢复Excel正常模式
                    ETExcelExtensions.SetAppNormalMode(true);
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error("提取方向角和下倾角失败", ex);
                return new ExtractionResult
                {
                    Success = false,
                    ErrorMessage = $"提取过程中发生错误：{ex.Message}"
                };
            }
        }

        /// <summary>
        /// 快速提取方向角和下倾角（使用默认参数）
        /// </summary>
        /// <param name="sourceRange">源数据范围</param>
        /// <param name="azimuthOutputRange">方向角输出范围</param>
        /// <param name="tiltAngleOutputRange">下倾角输出范围</param>
        /// <returns>提取结果</returns>
        public static ExtractionResult QuickExtractAngles(Range sourceRange, Range azimuthOutputRange, Range tiltAngleOutputRange)
        {
            var parameters = new ExtractionParameters
            {
                SourceRange = sourceRange,
                AzimuthOutputRange = azimuthOutputRange,
                TiltAngleOutputRange = tiltAngleOutputRange
            };

            return ExtractAngles(parameters);
        }

        /// <summary>
        /// 获取功能说明信息
        /// </summary>
        /// <returns>功能说明</returns>
        public static string GetFunctionDescription()
        {
            return "本功能：用于提取方向角和下倾角，并填写到指定列\r\n" +
                   "注意：只有1小区的数据无法识别，该功能目前有Bug，不是很准确\r\n\r\n" +
                   "使用方法：\r\n" +
                   "1. 选择包含原始数据的来源范围\r\n" +
                   "2. 选择方向角输出列（可选）\r\n" +
                   "3. 选择下倾角输出列（可选）\r\n" +
                   "4. 点击提取按钮执行操作\r\n\r\n" +
                   "提取规则：\r\n" +
                   "- 方向角：识别格式如 120/240/360 的数字组合\r\n" +
                   "- 下倾角：识别范围在-5到20度之间的角度值\r\n" +
                   "- 自动跳过筛选行和标题行";
        }
    }
}