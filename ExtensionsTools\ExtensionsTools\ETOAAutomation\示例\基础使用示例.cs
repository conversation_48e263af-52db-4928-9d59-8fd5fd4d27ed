using System;
using System.Threading.Tasks;
using System.Collections.Generic;
using ExtensionsTools.ETOAAutomation;
using ExtensionsTools.ETOAAutomation.Models;
using ET;

namespace ETOAAutomation.Examples
{
    /// <summary>
    /// ETOAAutomation 基础使用示例
    /// 演示如何使用ETOAClient进行基本的OA系统操作
    /// </summary>
    public class BasicUsageExample
    {
        private ETOAClient _client;

        /// <summary>
        /// 运行基础示例
        /// </summary>
        public async Task RunBasicExampleAsync()
        {
            try
            {
                Console.WriteLine("=== ETOAAutomation 基础使用示例 ===");
                
                // 1. 初始化客户端
                await InitializeClientAsync();
                
                // 2. 登录系统
                await LoginAsync();
                
                // 3. 执行API操作
                await PerformApiOperationsAsync();
                
                // 4. 文件上传操作
                await PerformFileUploadAsync();
                
                // 5. 浏览器模拟操作
                await PerformBrowserOperationsAsync();
                
                // 6. 会话管理操作
                await PerformSessionManagementAsync();
                
                // 7. 配置管理操作
                PerformConfigurationManagement();
                
                // 8. 性能监控
                await PerformPerformanceMonitoringAsync();
                
                Console.WriteLine("=== 示例执行完成 ===");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"示例执行失败: {ex.Message}");
                ETLogManager.Error("基础示例执行失败", ex);
            }
            finally
            {
                // 清理资源
                await CleanupAsync();
            }
        }

        /// <summary>
        /// 初始化客户端
        /// </summary>
        private async Task InitializeClientAsync()
        {
            Console.WriteLine("\n1. 初始化客户端...");
            
            // 创建客户端实例
            _client = new ETOAClient("https://your-oa-system.com");
            
            // 订阅事件
            _client.GlobalException += OnGlobalException;
            _client.LoginStatusChanged += OnLoginStatusChanged;
            _client.PerformanceAlert += OnPerformanceAlert;
            
            Console.WriteLine("客户端初始化完成");
        }

        /// <summary>
        /// 登录系统
        /// </summary>
        private async Task LoginAsync()
        {
            Console.WriteLine("\n2. 登录系统...");
            
            // 方式1: 自动登录（推荐）
            bool loginSuccess = await _client.LoginAsync("your_username", "your_password");
            
            if (loginSuccess)
            {
                Console.WriteLine("登录成功!");
                Console.WriteLine($"用户ID: {_client.LoginInfo.UserId}");
                Console.WriteLine($"用户名: {_client.LoginInfo.Username}");
                Console.WriteLine($"认证令牌: {_client.LoginInfo.Token?.Substring(0, 10)}...");
            }
            else
            {
                throw new Exception("登录失败");
            }
        }

        /// <summary>
        /// 执行API操作
        /// </summary>
        private async Task PerformApiOperationsAsync()
        {
            Console.WriteLine("\n3. 执行API操作...");
            
            try
            {
                // GET请求示例 - 获取用户信息
                Console.WriteLine("  3.1 获取用户信息...");
                var userInfo = await _client.ApiClient.GetAsync<object>("/api/user/profile");
                Console.WriteLine($"  用户信息获取成功: {userInfo != null}");
                
                // POST请求示例 - 创建文档
                Console.WriteLine("  3.2 创建文档...");
                var documentData = new
                {
                    title = "测试文档",
                    content = "这是一个测试文档的内容",
                    category = "测试分类",
                    tags = new[] { "测试", "示例" }
                };
                
                var createResult = await _client.ApiClient.PostAsync<object>("/api/documents", documentData);
                Console.WriteLine($"  文档创建成功: {createResult != null}");
                
                // GET请求带参数示例 - 搜索文档
                Console.WriteLine("  3.3 搜索文档...");
                var searchParams = new Dictionary<string, object>
                {
                    ["keyword"] = "测试",
                    ["page"] = 1,
                    ["size"] = 10
                };
                
                var searchResult = await _client.ApiClient.GetAsync<object>("/api/documents/search", searchParams);
                Console.WriteLine($"  文档搜索成功: {searchResult != null}");
                
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  API操作失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 执行文件上传操作
        /// </summary>
        private async Task PerformFileUploadAsync()
        {
            Console.WriteLine("\n4. 文件上传操作...");
            
            try
            {
                // 创建测试文件
                string testFilePath = CreateTestFile();
                
                // 订阅上传进度事件
                _client.FileUploader.UploadProgress += (sender, e) =>
                {
                    Console.WriteLine($"  上传进度: {e.ProgressPercentage}%");
                };
                
                // 单文件上传
                Console.WriteLine("  4.1 单文件上传...");
                var uploadResult = await _client.FileUploader.UploadFileAsync(
                    "/api/upload", 
                    testFilePath,
                    "document"
                );
                
                if (uploadResult.Success)
                {
                    Console.WriteLine($"  文件上传成功，文件ID: {uploadResult.FileId}");
                }
                else
                {
                    Console.WriteLine($"  文件上传失败: {uploadResult.ErrorMessage}");
                }
                
                // 清理测试文件
                if (System.IO.File.Exists(testFilePath))
                {
                    System.IO.File.Delete(testFilePath);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  文件上传操作失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 执行浏览器模拟操作
        /// </summary>
        private async Task PerformBrowserOperationsAsync()
        {
            Console.WriteLine("\n5. 浏览器模拟操作...");
            
            try
            {
                // 创建模拟浏览器
                var browser = _client.CreateSimulationBrowser("https://your-oa-system.com/workflow");
                
                // 等待页面加载
                Console.WriteLine("  5.1 等待页面加载...");
                await browser.WaitForPageLoadAsync();
                
                // 页面操作示例
                Console.WriteLine("  5.2 执行页面操作...");
                
                // 输入文本
                await browser.SetElementValueAsync("#title-input", "自动化测试标题");
                
                // 选择下拉框
                await browser.SelectOptionAsync("#category-select", "工作文档");
                
                // 点击按钮
                await browser.ClickElementAsync("#save-button");
                
                // 等待操作完成
                await Task.Delay(2000);
                
                Console.WriteLine("  浏览器操作完成");
                
                // 释放浏览器资源
                browser.Dispose();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  浏览器操作失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 执行会话管理操作
        /// </summary>
        private async Task PerformSessionManagementAsync()
        {
            Console.WriteLine("\n6. 会话管理操作...");
            
            try
            {
                // 配置会话管理
                _client.SessionManager.HeartbeatInterval = 300; // 5分钟心跳
                _client.SessionManager.AutoReloginEnabled = true;
                _client.SessionManager.MaxReloginAttempts = 3;
                
                // 获取会话统计信息
                var sessionStats = _client.GetSessionStatistics();
                
                Console.WriteLine($"  会话状态: {(sessionStats.IsActive ? "活跃" : "非活跃")}");
                Console.WriteLine($"  会话ID: {sessionStats.CurrentSessionId}");
                Console.WriteLine($"  最后心跳: {sessionStats.LastHeartbeat}");
                Console.WriteLine($"  会话存活时间: {sessionStats.SessionAge}");
                Console.WriteLine($"  心跳间隔: {sessionStats.HeartbeatInterval}");
                Console.WriteLine($"  是否正在监控: {sessionStats.IsMonitoring}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  会话管理操作失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 执行配置管理操作
        /// </summary>
        private void PerformConfigurationManagement()
        {
            Console.WriteLine("\n7. 配置管理操作...");
            
            try
            {
                // 设置配置值
                _client.SetConfig("Application", "Name", "ETOAAutomation示例");
                _client.SetConfig("Network", "Timeout", "60");
                _client.SetConfig("Upload", "MaxFileSize", "10485760"); // 10MB
                
                // 读取配置值
                string appName = _client.GetConfig("Application", "Name", "默认应用");
                int timeout = int.Parse(_client.GetConfig("Network", "Timeout", "30"));
                long maxFileSize = long.Parse(_client.GetConfig("Upload", "MaxFileSize", "5242880"));
                
                Console.WriteLine($"  应用名称: {appName}");
                Console.WriteLine($"  网络超时: {timeout}秒");
                Console.WriteLine($"  最大文件大小: {maxFileSize / 1024 / 1024}MB");
                
                // 全局设置
                _client.SetGlobalSetting("debug_mode", true);
                _client.SetGlobalSetting("max_retry_count", 3);
                
                bool debugMode = _client.GetGlobalSetting<bool>("debug_mode", false);
                int maxRetry = _client.GetGlobalSetting<int>("max_retry_count", 1);
                
                Console.WriteLine($"  调试模式: {debugMode}");
                Console.WriteLine($"  最大重试次数: {maxRetry}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  配置管理操作失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 执行性能监控
        /// </summary>
        private async Task PerformPerformanceMonitoringAsync()
        {
            Console.WriteLine("\n8. 性能监控...");
            
            try
            {
                // 获取性能统计信息
                var perfStats = _client.GetPerformanceStatistics();
                
                Console.WriteLine($"  CPU使用率: {perfStats.CpuUsage}%");
                Console.WriteLine($"  内存使用: {perfStats.MemoryUsage} MB");
                Console.WriteLine($"  网络延迟: {perfStats.NetworkLatency} ms");
                Console.WriteLine($"  系统健康状态: {(perfStats.IsHealthy ? "正常" : "异常")}");
                
                // 执行健康检查
                Console.WriteLine("  8.1 执行健康检查...");
                var healthResult = await _client.PerformHealthCheckAsync();
                
                Console.WriteLine($"  整体健康状态: {(healthResult.IsHealthy ? "健康" : "异常")}");
                Console.WriteLine($"  检查时间: {healthResult.Timestamp}");
                
                if (healthResult.Details.Count > 0)
                {
                    Console.WriteLine("  详细信息:");
                    foreach (var detail in healthResult.Details)
                    {
                        Console.WriteLine($"    {detail.Key}: {detail.Value}");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  性能监控失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 清理资源
        /// </summary>
        private async Task CleanupAsync()
        {
            Console.WriteLine("\n9. 清理资源...");
            
            try
            {
                if (_client != null)
                {
                    // 登出系统
                    if (_client.IsLoggedIn)
                    {
                        await _client.LogoutAsync();
                    }
                    
                    // 释放资源
                    _client.Dispose();
                }
                
                Console.WriteLine("资源清理完成");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"资源清理失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 创建测试文件
        /// </summary>
        private string CreateTestFile()
        {
            string tempPath = System.IO.Path.GetTempPath();
            string fileName = $"test_file_{DateTime.Now:yyyyMMdd_HHmmss}.txt";
            string filePath = System.IO.Path.Combine(tempPath, fileName);
            
            string content = $"这是一个测试文件\n创建时间: {DateTime.Now}\n文件大小: 约1KB";
            System.IO.File.WriteAllText(filePath, content);
            
            return filePath;
        }

        #region 事件处理方法

        /// <summary>
        /// 全局异常事件处理
        /// </summary>
        private void OnGlobalException(object sender, GlobalExceptionEventArgs e)
        {
            Console.WriteLine($"[全局异常] {e.Source}: {e.Exception.Message}");
            e.Handled = true; // 标记为已处理
        }

        /// <summary>
        /// 登录状态变更事件处理
        /// </summary>
        private void OnLoginStatusChanged(object sender, LoginStatusChangedEventArgs e)
        {
            string status = e.IsLoggedIn ? "已登录" : "已登出";
            Console.WriteLine($"[状态变更] 用户 {e.Username} {status}");
        }

        /// <summary>
        /// 性能警报事件处理
        /// </summary>
        private void OnPerformanceAlert(object sender, PerformanceEventArgs e)
        {
            Console.WriteLine($"[性能警报] {e.Severity}: {e.Message}");
        }

        #endregion
    }

    /// <summary>
    /// 程序入口点
    /// </summary>
    public class Program
    {
        public static async Task Main(string[] args)
        {
            try
            {
                var example = new BasicUsageExample();
                await example.RunBasicExampleAsync();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"程序执行失败: {ex.Message}");
            }
            
            Console.WriteLine("\n按任意键退出...");
            Console.ReadKey();
        }
    }
}
