using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Windows.Forms;
using ET.ETOAAutomation.Models;
using ET;

namespace ET.ETOAAutomation.Helpers
{
    /// <summary>
    /// 本地存储辅助类，提供数据的安全存储和管理功能
    /// </summary>
    public static class ETOAStorageHelper
    {
        #region 私有字段
        /// <summary>
        /// 存储根目录
        /// </summary>
        private static readonly string _storageRoot = Path.Combine(
            Application.StartupPath, "Data", "ETOAStorage");

        /// <summary>
        /// 认证信息存储目录
        /// </summary>
        private static readonly string _authStoragePath = Path.Combine(_storageRoot, "Auth");

        /// <summary>
        /// 会话数据存储目录
        /// </summary>
        private static readonly string _sessionStoragePath = Path.Combine(_storageRoot, "Session");

        /// <summary>
        /// 缓存数据存储目录
        /// </summary>
        private static readonly string _cacheStoragePath = Path.Combine(_storageRoot, "Cache");

        /// <summary>
        /// 加密密钥
        /// </summary>
        private static readonly string _encryptionKey = GenerateMachineKey();
        #endregion

        #region 初始化方法
        /// <summary>
        /// 初始化存储目录
        /// </summary>
        public static void Initialize()
        {
            try
            {
                // 创建存储目录
                EnsureDirectoryExists(_storageRoot);
                EnsureDirectoryExists(_authStoragePath);
                EnsureDirectoryExists(_sessionStoragePath);
                EnsureDirectoryExists(_cacheStoragePath);

                ETLogManager.Info($"存储目录初始化完成: {_storageRoot}");
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"初始化存储目录失败: {ex.Message}", ex);
                throw new ETException($"初始化存储目录失败: {ex.Message}", "Initialize", ex);
            }
        }

        /// <summary>
        /// 确保目录存在
        /// </summary>
        /// <param name="directoryPath">目录路径</param>
        private static void EnsureDirectoryExists(string directoryPath)
        {
            if (!Directory.Exists(directoryPath))
            {
                Directory.CreateDirectory(directoryPath);
                ETLogManager.Info($"创建目录: {directoryPath}");
            }
        }
        #endregion

        #region 认证信息存储
        /// <summary>
        /// 保存认证信息
        /// </summary>
        /// <param name="username">用户名</param>
        /// <param name="loginInfo">登录信息</param>
        public static void SaveAuthInfo(string username, ETOALoginInfo loginInfo)
        {
            try
            {
                if (string.IsNullOrEmpty(username) || loginInfo == null) return;

                var filePath = Path.Combine(_authStoragePath, $"{SanitizeFileName(username)}.auth");
                var jsonData = ETOAJsonHelper.ToJson(loginInfo);
                var encryptedData = EncryptString(jsonData);

                File.WriteAllText(filePath, encryptedData, Encoding.UTF8);
                ETLogManager.Info($"认证信息已保存: {username}");
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"保存认证信息失败: {ex.Message}", ex);
                throw new ETException($"保存认证信息失败: {ex.Message}", "SaveAuthInfo", ex);
            }
        }

        /// <summary>
        /// 加载认证信息
        /// </summary>
        /// <param name="username">用户名</param>
        /// <returns>登录信息</returns>
        public static ETOALoginInfo LoadAuthInfo(string username)
        {
            try
            {
                if (string.IsNullOrEmpty(username)) return null;

                var filePath = Path.Combine(_authStoragePath, $"{SanitizeFileName(username)}.auth");
                if (!File.Exists(filePath)) return null;

                var encryptedData = File.ReadAllText(filePath, Encoding.UTF8);
                var jsonData = DecryptString(encryptedData);
                var loginInfo = ETOAJsonHelper.FromJsonSafe<ETOALoginInfo>(jsonData);

                ETLogManager.Info($"认证信息已加载: {username}");
                return loginInfo;
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"加载认证信息失败: {ex.Message}", ex);
                return null;
            }
        }

        /// <summary>
        /// 删除认证信息
        /// </summary>
        /// <param name="username">用户名</param>
        /// <returns>是否成功删除</returns>
        public static bool DeleteAuthInfo(string username)
        {
            try
            {
                if (string.IsNullOrEmpty(username)) return false;

                var filePath = Path.Combine(_authStoragePath, $"{SanitizeFileName(username)}.auth");
                if (File.Exists(filePath))
                {
                    File.Delete(filePath);
                    ETLogManager.Info($"认证信息已删除: {username}");
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"删除认证信息失败: {ex.Message}", ex);
                return false;
            }
        }

        /// <summary>
        /// 获取所有已保存的用户名
        /// </summary>
        /// <returns>用户名列表</returns>
        public static List<string> GetSavedUsernames()
        {
            try
            {
                if (!Directory.Exists(_authStoragePath)) return new List<string>();

                var files = Directory.GetFiles(_authStoragePath, "*.auth");
                var usernames = files.Select(f => Path.GetFileNameWithoutExtension(f))
                                    .Where(name => !string.IsNullOrEmpty(name))
                                    .ToList();

                ETLogManager.Info($"获取到{usernames.Count}个已保存的用户名");
                return usernames;
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"获取用户名列表失败: {ex.Message}", ex);
                return new List<string>();
            }
        }
        #endregion

        #region 会话数据存储
        /// <summary>
        /// 保存会话数据
        /// </summary>
        /// <param name="sessionId">会话ID</param>
        /// <param name="sessionData">会话数据</param>
        public static void SaveSessionData(string sessionId, ETOASessionData sessionData)
        {
            try
            {
                if (string.IsNullOrEmpty(sessionId) || sessionData == null) return;

                var filePath = Path.Combine(_sessionStoragePath, $"{SanitizeFileName(sessionId)}.session");
                var jsonData = ETOAJsonHelper.ToJson(sessionData);
                var encryptedData = EncryptString(jsonData);

                File.WriteAllText(filePath, encryptedData, Encoding.UTF8);
                ETLogManager.Info($"会话数据已保存: {sessionId}");
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"保存会话数据失败: {ex.Message}", ex);
                throw new ETException($"保存会话数据失败: {ex.Message}", "SaveSessionData", ex);
            }
        }

        /// <summary>
        /// 加载会话数据
        /// </summary>
        /// <param name="sessionId">会话ID</param>
        /// <returns>会话数据</returns>
        public static ETOASessionData LoadSessionData(string sessionId)
        {
            try
            {
                if (string.IsNullOrEmpty(sessionId)) return null;

                var filePath = Path.Combine(_sessionStoragePath, $"{SanitizeFileName(sessionId)}.session");
                if (!File.Exists(filePath)) return null;

                var encryptedData = File.ReadAllText(filePath, Encoding.UTF8);
                var jsonData = DecryptString(encryptedData);
                var sessionData = ETOAJsonHelper.FromJsonSafe<ETOASessionData>(jsonData);

                ETLogManager.Info($"会话数据已加载: {sessionId}");
                return sessionData;
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"加载会话数据失败: {ex.Message}", ex);
                return null;
            }
        }

        /// <summary>
        /// 删除会话数据
        /// </summary>
        /// <param name="sessionId">会话ID</param>
        /// <returns>是否成功删除</returns>
        public static bool DeleteSessionData(string sessionId)
        {
            try
            {
                if (string.IsNullOrEmpty(sessionId)) return false;

                var filePath = Path.Combine(_sessionStoragePath, $"{SanitizeFileName(sessionId)}.session");
                if (File.Exists(filePath))
                {
                    File.Delete(filePath);
                    ETLogManager.Info($"会话数据已删除: {sessionId}");
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"删除会话数据失败: {ex.Message}", ex);
                return false;
            }
        }

        /// <summary>
        /// 获取所有会话ID
        /// </summary>
        /// <returns>会话ID列表</returns>
        public static List<string> GetAllSessionIds()
        {
            try
            {
                if (!Directory.Exists(_sessionStoragePath)) return new List<string>();

                var files = Directory.GetFiles(_sessionStoragePath, "*.session");
                var sessionIds = files.Select(f => Path.GetFileNameWithoutExtension(f))
                                     .Where(id => !string.IsNullOrEmpty(id))
                                     .ToList();

                ETLogManager.Info($"获取到{sessionIds.Count}个会话ID");
                return sessionIds;
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"获取会话ID列表失败: {ex.Message}", ex);
                return new List<string>();
            }
        }
        #endregion

        #region 缓存数据存储
        /// <summary>
        /// 保存缓存数据
        /// </summary>
        /// <param name="cacheKey">缓存键</param>
        /// <param name="data">数据</param>
        /// <param name="expirationTime">过期时间</param>
        public static void SaveCacheData(string cacheKey, object data, DateTime expirationTime)
        {
            try
            {
                if (string.IsNullOrEmpty(cacheKey) || data == null) return;

                var cacheItem = new
                {
                    Data = data,
                    ExpirationTime = expirationTime,
                    CreatedTime = DateTime.Now
                };

                var filePath = Path.Combine(_cacheStoragePath, $"{SanitizeFileName(cacheKey)}.cache");
                var jsonData = ETOAJsonHelper.ToJson(cacheItem);

                File.WriteAllText(filePath, jsonData, Encoding.UTF8);
                ETLogManager.Info($"缓存数据已保存: {cacheKey}");
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"保存缓存数据失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 加载缓存数据
        /// </summary>
        /// <typeparam name="T">数据类型</typeparam>
        /// <param name="cacheKey">缓存键</param>
        /// <returns>缓存数据</returns>
        public static T LoadCacheData<T>(string cacheKey)
        {
            try
            {
                if (string.IsNullOrEmpty(cacheKey)) return default(T);

                var filePath = Path.Combine(_cacheStoragePath, $"{SanitizeFileName(cacheKey)}.cache");
                if (!File.Exists(filePath)) return default(T);

                var jsonData = File.ReadAllText(filePath, Encoding.UTF8);
                var cacheItem = ETOAJsonHelper.FromJsonDynamic(jsonData);

                // 检查是否过期
                if (cacheItem?.ExpirationTime != null)
                {
                    var expirationTime = DateTime.Parse(cacheItem.ExpirationTime.ToString());
                    if (DateTime.Now > expirationTime)
                    {
                        // 缓存已过期，删除文件
                        File.Delete(filePath);
                        ETLogManager.Info($"缓存已过期并删除: {cacheKey}");
                        return default(T);
                    }
                }

                if (cacheItem?.Data != null)
                {
                    var dataJson = ETOAJsonHelper.ToJson(cacheItem.Data);
                    var result = ETOAJsonHelper.FromJsonSafe<T>(dataJson);
                    ETLogManager.Info($"缓存数据已加载: {cacheKey}");
                    return result;
                }

                return default(T);
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"加载缓存数据失败: {ex.Message}", ex);
                return default(T);
            }
        }

        /// <summary>
        /// 清理过期缓存
        /// </summary>
        /// <returns>清理的缓存数量</returns>
        public static int CleanExpiredCache()
        {
            try
            {
                if (!Directory.Exists(_cacheStoragePath)) return 0;

                var files = Directory.GetFiles(_cacheStoragePath, "*.cache");
                int cleanedCount = 0;

                foreach (var file in files)
                {
                    try
                    {
                        var jsonData = File.ReadAllText(file, Encoding.UTF8);
                        var cacheItem = ETOAJsonHelper.FromJsonDynamic(jsonData);

                        if (cacheItem?.ExpirationTime != null)
                        {
                            var expirationTime = DateTime.Parse(cacheItem.ExpirationTime.ToString());
                            if (DateTime.Now > expirationTime)
                            {
                                File.Delete(file);
                                cleanedCount++;
                            }
                        }
                    }
                    catch
                    {
                        // 如果文件损坏，也删除它
                        File.Delete(file);
                        cleanedCount++;
                    }
                }

                ETLogManager.Info($"清理过期缓存完成，共清理{cleanedCount}个文件");
                return cleanedCount;
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"清理过期缓存失败: {ex.Message}", ex);
                return 0;
            }
        }
        #endregion

        #region 加密解密方法
        /// <summary>
        /// 加密字符串
        /// </summary>
        /// <param name="plainText">明文</param>
        /// <returns>密文</returns>
        private static string EncryptString(string plainText)
        {
            if (!ETOAConfigHelper.GetEncryptionEnabled()) return plainText;

            try
            {
                var keyBytes = Encoding.UTF8.GetBytes(_encryptionKey);
                var plainBytes = Encoding.UTF8.GetBytes(plainText);
                var encryptedBytes = new byte[plainBytes.Length];

                for (int i = 0; i < plainBytes.Length; i++)
                {
                    encryptedBytes[i] = (byte)(plainBytes[i] ^ keyBytes[i % keyBytes.Length]);
                }

                return Convert.ToBase64String(encryptedBytes);
            }
            catch (Exception ex)
            {
                ETLogManager.Warn($"字符串加密失败: {ex.Message}");
                return plainText; // 加密失败时返回原文
            }
        }

        /// <summary>
        /// 解密字符串
        /// </summary>
        /// <param name="encryptedText">密文</param>
        /// <returns>明文</returns>
        private static string DecryptString(string encryptedText)
        {
            if (!ETOAConfigHelper.GetEncryptionEnabled()) return encryptedText;

            try
            {
                var keyBytes = Encoding.UTF8.GetBytes(_encryptionKey);
                var encryptedBytes = Convert.FromBase64String(encryptedText);
                var decryptedBytes = new byte[encryptedBytes.Length];

                for (int i = 0; i < encryptedBytes.Length; i++)
                {
                    decryptedBytes[i] = (byte)(encryptedBytes[i] ^ keyBytes[i % keyBytes.Length]);
                }

                return Encoding.UTF8.GetString(decryptedBytes);
            }
            catch (Exception ex)
            {
                ETLogManager.Warn($"字符串解密失败: {ex.Message}");
                return encryptedText; // 解密失败时返回原文
            }
        }

        /// <summary>
        /// 生成机器唯一标识作为加密密钥
        /// </summary>
        /// <returns>加密密钥</returns>
        private static string GenerateMachineKey()
        {
            try
            {
                var machineId = Environment.MachineName + Environment.UserName + Environment.OSVersion.ToString();
                using (var sha256 = SHA256.Create())
                {
                    var hash = sha256.ComputeHash(Encoding.UTF8.GetBytes(machineId));
                    return Convert.ToBase64String(hash).Substring(0, 16);
                }
            }
            catch
            {
                return "DefaultKey123456"; // 备用密钥
            }
        }
        #endregion

        #region 辅助方法
        /// <summary>
        /// 清理文件名中的非法字符
        /// </summary>
        /// <param name="fileName">原始文件名</param>
        /// <returns>清理后的文件名</returns>
        private static string SanitizeFileName(string fileName)
        {
            if (string.IsNullOrEmpty(fileName)) return "default";

            var invalidChars = Path.GetInvalidFileNameChars();
            var sanitized = new StringBuilder();

            foreach (char c in fileName)
            {
                if (!invalidChars.Contains(c))
                {
                    sanitized.Append(c);
                }
                else
                {
                    sanitized.Append('_');
                }
            }

            return sanitized.ToString();
        }

        /// <summary>
        /// 获取存储统计信息
        /// </summary>
        /// <returns>存储统计</returns>
        public static (int AuthFiles, int SessionFiles, int CacheFiles, long TotalSize) GetStorageStats()
        {
            try
            {
                int authFiles = Directory.Exists(_authStoragePath) ? Directory.GetFiles(_authStoragePath, "*.auth").Length : 0;
                int sessionFiles = Directory.Exists(_sessionStoragePath) ? Directory.GetFiles(_sessionStoragePath, "*.session").Length : 0;
                int cacheFiles = Directory.Exists(_cacheStoragePath) ? Directory.GetFiles(_cacheStoragePath, "*.cache").Length : 0;

                long totalSize = 0;
                if (Directory.Exists(_storageRoot))
                {
                    var allFiles = Directory.GetFiles(_storageRoot, "*", SearchOption.AllDirectories);
                    totalSize = allFiles.Sum(f => new FileInfo(f).Length);
                }

                return (authFiles, sessionFiles, cacheFiles, totalSize);
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"获取存储统计失败: {ex.Message}", ex);
                return (0, 0, 0, 0);
            }
        }

        /// <summary>
        /// 清理所有存储数据
        /// </summary>
        /// <returns>是否成功清理</returns>
        public static bool ClearAllStorage()
        {
            try
            {
                if (Directory.Exists(_storageRoot))
                {
                    Directory.Delete(_storageRoot, true);
                    ETLogManager.Info("所有存储数据已清理");
                }

                // 重新初始化目录
                Initialize();
                return true;
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"清理存储数据失败: {ex.Message}", ex);
                return false;
            }
        }
        #endregion
    }
}
