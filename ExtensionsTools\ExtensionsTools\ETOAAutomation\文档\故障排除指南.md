# ETOAAutomation 故障排除指南

## 概述

本指南提供了使用ETOAAutomation类库时可能遇到的常见问题及其解决方案。

## 1. 登录相关问题

### 1.1 登录失败

**问题描述**: 调用 `LoginAsync()` 方法返回 `false`

**可能原因**:
- 用户名或密码错误
- OA系统URL不正确或无法访问
- 网络连接问题
- OA系统登录页面结构发生变化

**解决方案**:

```csharp
// 1. 检查基本连接
try
{
    var client = new ETOAClient("https://your-oa-system.com");
    
    // 测试网络连接
    var healthCheck = await client.PerformHealthCheckAsync();
    if (!healthCheck.IsHealthy)
    {
        Console.WriteLine("网络连接异常:");
        foreach (var detail in healthCheck.Details)
        {
            Console.WriteLine($"  {detail.Key}: {detail.Value}");
        }
    }
}
catch (Exception ex)
{
    Console.WriteLine($"连接失败: {ex.Message}");
}

// 2. 启用详细日志
ETLogManager.SetLogLevel(LogLevel.Debug);

// 3. 检查登录信息
var loginResult = await client.LoginAsync("username", "password");
if (!loginResult)
{
    // 查看详细错误信息
    var loginInfo = client.LoginInfo;
    if (loginInfo != null)
    {
        Console.WriteLine($"登录错误: {loginInfo.ErrorMessage}");
    }
}
```

### 1.2 自动登录失败，但手动登录成功

**问题描述**: 自动登录失败，但显示登录对话框可以成功登录

**可能原因**:
- 登录页面有验证码
- 登录页面有额外的安全验证
- 页面元素定位器需要更新

**解决方案**:

```csharp
// 1. 检查是否需要验证码
var browser = client.CreateSimulationBrowser();
await browser.NavigateToAsync("https://your-oa-system.com/login");

// 检查验证码元素
if (await browser.IsElementVisibleAsync("#captcha-image"))
{
    Console.WriteLine("登录页面包含验证码，需要手动处理");
    // 使用登录对话框
    var loginInfo = await browser.ShowLoginDialogAsync();
}

// 2. 更新元素定位器
// 如果自动登录失败，可能需要更新ETOALoginBrowser中的元素选择器
```

### 1.3 会话过期问题

**问题描述**: 登录成功后，会话很快过期

**解决方案**:

```csharp
// 1. 调整心跳间隔
client.SessionManager.HeartbeatInterval = 180; // 3分钟

// 2. 启用自动重登
client.SessionManager.AutoReloginEnabled = true;
client.SessionManager.MaxReloginAttempts = 5;

// 3. 监控会话状态
client.SessionManager.SessionStatusChanged += (sender, e) =>
{
    Console.WriteLine($"会话状态变更: {e.OldStatus} -> {e.NewStatus}");
    if (e.NewStatus == SessionStatus.Inactive)
    {
        Console.WriteLine("会话已失效，检查网络连接和服务器状态");
    }
};

// 4. 手动检查会话有效性
var isValid = await client.SessionManager.IsSessionValidAsync();
if (!isValid)
{
    Console.WriteLine("会话无效，尝试刷新");
    await client.SessionManager.RefreshSessionAsync();
}
```

## 2. API调用问题

### 2.1 API请求超时

**问题描述**: API调用时出现超时异常

**解决方案**:

```csharp
// 1. 增加超时时间
client.SetConfig("Network", "Timeout", "120"); // 2分钟

// 2. 实现重试机制
public async Task<T> ApiCallWithRetryAsync<T>(string endpoint, int maxRetries = 3)
{
    for (int i = 0; i < maxRetries; i++)
    {
        try
        {
            return await client.ApiClient.GetAsync<T>(endpoint);
        }
        catch (TimeoutException ex) when (i < maxRetries - 1)
        {
            Console.WriteLine($"API调用超时，第 {i + 1} 次重试");
            await Task.Delay(TimeSpan.FromSeconds(Math.Pow(2, i))); // 指数退避
        }
    }
    throw new Exception($"API调用失败，已重试 {maxRetries} 次");
}

// 3. 检查网络状态
var perfStats = client.GetPerformanceStatistics();
if (perfStats.NetworkLatency > 5000)
{
    Console.WriteLine($"网络延迟过高: {perfStats.NetworkLatency}ms");
}
```

### 2.2 认证失败

**问题描述**: API调用返回401未授权错误

**解决方案**:

```csharp
// 1. 检查登录状态
if (!client.IsLoggedIn)
{
    Console.WriteLine("用户未登录，请先登录");
    await client.LoginAsync("username", "password");
}

// 2. 检查令牌有效性
if (client.LoginInfo?.Token != null)
{
    // 检查令牌是否过期
    if (client.LoginInfo.ExpiryTime < DateTime.Now)
    {
        Console.WriteLine("认证令牌已过期，尝试刷新");
        await client.SessionManager.RefreshSessionAsync();
    }
}

// 3. 手动刷新认证
try
{
    var result = await client.ApiClient.GetAsync<object>("/api/user/profile");
}
catch (UnauthorizedAccessException)
{
    Console.WriteLine("认证失败，尝试重新登录");
    await client.LogoutAsync();
    await client.LoginAsync("username", "password");
}
```

### 2.3 数据格式错误

**问题描述**: API返回的数据无法正确反序列化

**解决方案**:

```csharp
// 1. 启用原始响应日志
ETLogManager.SetLogLevel(LogLevel.Debug);

// 2. 检查响应格式
try
{
    var response = await client.ApiClient.GetAsync<string>("/api/data");
    Console.WriteLine($"原始响应: {response}");
    
    // 手动解析
    var data = JsonConvert.DeserializeObject<YourDataModel>(response);
}
catch (JsonException ex)
{
    Console.WriteLine($"JSON解析失败: {ex.Message}");
    // 检查API文档，确认数据格式
}

// 3. 使用动态类型
var dynamicResult = await client.ApiClient.GetAsync<dynamic>("/api/data");
Console.WriteLine($"动态结果: {JsonConvert.SerializeObject(dynamicResult, Formatting.Indented)}");
```

## 3. 文件上传问题

### 3.1 文件上传失败

**问题描述**: 文件上传返回失败状态

**解决方案**:

```csharp
// 1. 检查文件基本信息
public bool ValidateFileForUpload(string filePath)
{
    if (!File.Exists(filePath))
    {
        Console.WriteLine($"文件不存在: {filePath}");
        return false;
    }
    
    var fileInfo = new FileInfo(filePath);
    var maxSize = 10 * 1024 * 1024; // 10MB
    
    if (fileInfo.Length > maxSize)
    {
        Console.WriteLine($"文件过大: {fileInfo.Length} bytes (最大: {maxSize} bytes)");
        return false;
    }
    
    var allowedExtensions = new[] { ".pdf", ".doc", ".docx", ".xls", ".xlsx" };
    var extension = Path.GetExtension(filePath).ToLower();
    
    if (!allowedExtensions.Contains(extension))
    {
        Console.WriteLine($"不支持的文件类型: {extension}");
        return false;
    }
    
    return true;
}

// 2. 监控上传进度
client.FileUploader.UploadProgress += (sender, e) =>
{
    Console.WriteLine($"上传进度: {e.ProgressPercentage}% - {e.BytesUploaded}/{e.TotalBytes}");
    
    if (e.ProgressPercentage == 100 && !e.IsCompleted)
    {
        Console.WriteLine("上传完成，等待服务器处理...");
    }
};

// 3. 详细错误处理
var result = await client.FileUploader.UploadFileAsync("/api/upload", filePath);
if (!result.Success)
{
    Console.WriteLine($"上传失败: {result.ErrorMessage}");
    Console.WriteLine($"错误代码: {result.ErrorCode}");
    
    // 根据错误类型采取不同措施
    switch (result.ErrorCode)
    {
        case "FILE_TOO_LARGE":
            Console.WriteLine("建议: 压缩文件或分割上传");
            break;
        case "INVALID_FORMAT":
            Console.WriteLine("建议: 检查文件格式是否正确");
            break;
        case "NETWORK_ERROR":
            Console.WriteLine("建议: 检查网络连接，稍后重试");
            break;
    }
}
```

### 3.2 上传进度停滞

**问题描述**: 文件上传进度长时间不更新

**解决方案**:

```csharp
// 1. 实现超时检测
public async Task<UploadResult> UploadWithTimeoutAsync(string endpoint, string filePath, TimeSpan timeout)
{
    using (var cts = new CancellationTokenSource(timeout))
    {
        try
        {
            return await client.FileUploader.UploadFileAsync(endpoint, filePath, cancellationToken: cts.Token);
        }
        catch (OperationCanceledException)
        {
            Console.WriteLine("上传超时，可能是网络问题或文件过大");
            throw new TimeoutException("文件上传超时");
        }
    }
}

// 2. 分块上传大文件
public async Task<UploadResult> UploadLargeFileAsync(string filePath)
{
    var fileInfo = new FileInfo(filePath);
    if (fileInfo.Length > 50 * 1024 * 1024) // 50MB
    {
        Console.WriteLine("检测到大文件，使用分块上传");
        return await client.FileUploader.UploadFileInChunksAsync("/api/upload/chunked", filePath);
    }
    else
    {
        return await client.FileUploader.UploadFileAsync("/api/upload", filePath);
    }
}
```

## 4. 浏览器操作问题

### 4.1 页面元素找不到

**问题描述**: 浏览器操作时提示元素不存在

**解决方案**:

```csharp
// 1. 等待页面完全加载
await browser.WaitForPageLoadAsync();
await Task.Delay(2000); // 额外等待2秒

// 2. 等待特定元素出现
try
{
    await browser.WaitForElementAsync("#target-element", TimeSpan.FromSeconds(30));
}
catch (TimeoutException)
{
    Console.WriteLine("元素未在指定时间内出现，检查页面是否正确加载");
    
    // 获取页面源码进行调试
    var pageSource = await browser.GetPageSourceAsync();
    Console.WriteLine("页面源码片段:");
    Console.WriteLine(pageSource.Substring(0, Math.Min(500, pageSource.Length)));
}

// 3. 使用多种选择器策略
var selectors = new[]
{
    "#submit-button",
    "input[type='submit']",
    "button[name='submit']",
    ".submit-btn"
};

foreach (var selector in selectors)
{
    if (await browser.IsElementVisibleAsync(selector))
    {
        await browser.ClickElementAsync(selector);
        break;
    }
}
```

### 4.2 JavaScript执行失败

**问题描述**: 执行JavaScript代码时出现错误

**解决方案**:

```csharp
// 1. 检查JavaScript环境
try
{
    var result = await browser.ExecuteScriptAsync("return typeof jQuery !== 'undefined';");
    if (!(bool)result)
    {
        Console.WriteLine("页面未加载jQuery，可能需要等待或使用原生JavaScript");
    }
}
catch (Exception ex)
{
    Console.WriteLine($"JavaScript执行环境检查失败: {ex.Message}");
}

// 2. 使用安全的JavaScript执行
public async Task<object> SafeExecuteScriptAsync(string script)
{
    try
    {
        // 包装脚本以捕获错误
        var wrappedScript = $@"
            try {{
                return {script};
            }} catch (error) {{
                return {{ error: error.message }};
            }}
        ";
        
        var result = await browser.ExecuteScriptAsync(wrappedScript);
        
        // 检查是否有错误
        if (result is Dictionary<string, object> dict && dict.ContainsKey("error"))
        {
            throw new Exception($"JavaScript错误: {dict["error"]}");
        }
        
        return result;
    }
    catch (Exception ex)
    {
        Console.WriteLine($"JavaScript执行失败: {ex.Message}");
        throw;
    }
}

// 3. 分步执行复杂操作
// 将复杂的JavaScript操作分解为多个简单步骤
await browser.ExecuteScriptAsync("document.getElementById('form').style.display = 'block';");
await Task.Delay(500);
await browser.ExecuteScriptAsync("document.getElementById('submit').disabled = false;");
```

## 5. 性能问题

### 5.1 内存使用过高

**问题描述**: 应用程序内存使用持续增长

**解决方案**:

```csharp
// 1. 定期监控内存使用
public class MemoryMonitor
{
    private Timer _monitorTimer;
    
    public void StartMonitoring()
    {
        _monitorTimer = new Timer(CheckMemoryUsage, null, 
            TimeSpan.FromMinutes(1), TimeSpan.FromMinutes(1));
    }
    
    private void CheckMemoryUsage(object state)
    {
        var process = Process.GetCurrentProcess();
        var memoryMB = process.WorkingSet64 / 1024 / 1024;
        
        Console.WriteLine($"当前内存使用: {memoryMB} MB");
        
        if (memoryMB > 500) // 500MB
        {
            Console.WriteLine("内存使用过高，建议执行清理操作");
            GC.Collect();
            GC.WaitForPendingFinalizers();
            GC.Collect();
        }
    }
}

// 2. 正确释放资源
public async Task ProcessDocumentsAsync(List<string> documentIds)
{
    foreach (var id in documentIds)
    {
        using (var browser = client.CreateSimulationBrowser())
        {
            // 处理文档...
        } // 自动释放浏览器资源
        
        // 定期强制垃圾回收
        if (documentIds.IndexOf(id) % 10 == 0)
        {
            GC.Collect();
        }
    }
}

// 3. 限制并发操作
var semaphore = new SemaphoreSlim(3); // 最多3个并发操作
var tasks = documentIds.Select(async id =>
{
    await semaphore.WaitAsync();
    try
    {
        return await ProcessDocumentAsync(id);
    }
    finally
    {
        semaphore.Release();
    }
});

await Task.WhenAll(tasks);
```

### 5.2 响应速度慢

**问题描述**: 操作响应时间过长

**解决方案**:

```csharp
// 1. 性能分析
public async Task<T> MeasurePerformanceAsync<T>(Func<Task<T>> operation, string operationName)
{
    var stopwatch = Stopwatch.StartNew();
    try
    {
        var result = await operation();
        stopwatch.Stop();
        
        Console.WriteLine($"{operationName} 耗时: {stopwatch.ElapsedMilliseconds}ms");
        
        if (stopwatch.ElapsedMilliseconds > 5000) // 5秒
        {
            Console.WriteLine($"警告: {operationName} 响应时间过长");
        }
        
        return result;
    }
    catch (Exception ex)
    {
        stopwatch.Stop();
        Console.WriteLine($"{operationName} 失败，耗时: {stopwatch.ElapsedMilliseconds}ms, 错误: {ex.Message}");
        throw;
    }
}

// 2. 使用缓存
private readonly Dictionary<string, (DateTime expiry, object data)> _cache = new();

public async Task<T> GetWithCacheAsync<T>(string key, Func<Task<T>> factory, TimeSpan? expiry = null)
{
    var expiryTime = DateTime.Now.Add(expiry ?? TimeSpan.FromMinutes(5));
    
    if (_cache.TryGetValue(key, out var cached) && cached.expiry > DateTime.Now)
    {
        return (T)cached.data;
    }
    
    var result = await factory();
    _cache[key] = (expiryTime, result);
    
    return result;
}

// 3. 异步并行处理
public async Task<List<T>> ProcessInParallelAsync<T>(IEnumerable<string> items, 
    Func<string, Task<T>> processor, int maxConcurrency = 5)
{
    var semaphore = new SemaphoreSlim(maxConcurrency);
    var tasks = items.Select(async item =>
    {
        await semaphore.WaitAsync();
        try
        {
            return await processor(item);
        }
        finally
        {
            semaphore.Release();
        }
    });
    
    return (await Task.WhenAll(tasks)).ToList();
}
```

## 6. 配置问题

### 6.1 配置文件读取失败

**问题描述**: 无法读取配置文件或配置值不正确

**解决方案**:

```csharp
// 1. 检查配置文件路径
public void ValidateConfigFile()
{
    var configPath = Path.Combine(
        Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location),
        "Config", "ETOAAutomation.ini");
    
    if (!File.Exists(configPath))
    {
        Console.WriteLine($"配置文件不存在: {configPath}");
        CreateDefaultConfigFile(configPath);
    }
    else
    {
        Console.WriteLine($"配置文件路径: {configPath}");
    }
}

// 2. 创建默认配置
private void CreateDefaultConfigFile(string configPath)
{
    var defaultConfig = @"
[Server]
Url=https://your-oa-system.com
Timeout=60
RetryCount=3

[Upload]
MaxFileSize=10485760
AllowedExtensions=.pdf,.doc,.docx,.xls,.xlsx

[Session]
HeartbeatInterval=300
AutoReloginEnabled=true
MaxReloginAttempts=3
";
    
    Directory.CreateDirectory(Path.GetDirectoryName(configPath));
    File.WriteAllText(configPath, defaultConfig);
    Console.WriteLine("已创建默认配置文件");
}

// 3. 配置验证
public void ValidateConfiguration()
{
    var requiredSettings = new[]
    {
        ("Server", "Url"),
        ("Server", "Timeout"),
        ("Upload", "MaxFileSize")
    };
    
    foreach (var (section, key) in requiredSettings)
    {
        var value = client.GetConfig(section, key, null);
        if (string.IsNullOrEmpty(value))
        {
            Console.WriteLine($"缺少必需的配置项: [{section}] {key}");
        }
    }
}
```

## 7. 日志和调试

### 7.1 启用详细日志

```csharp
// 1. 设置日志级别
ETLogManager.SetLogLevel(LogLevel.Debug);

// 2. 查看日志文件
var logPath = ETLogManager.GetLogFilePath();
Console.WriteLine($"日志文件位置: {logPath}");

// 3. 实时监控日志
public void MonitorLogFile(string logPath)
{
    using (var fileStream = new FileStream(logPath, FileMode.Open, FileAccess.Read, FileShare.ReadWrite))
    using (var reader = new StreamReader(fileStream))
    {
        // 移动到文件末尾
        reader.BaseStream.Seek(0, SeekOrigin.End);
        
        while (true)
        {
            var line = reader.ReadLine();
            if (line != null)
            {
                Console.WriteLine($"[LOG] {line}");
            }
            else
            {
                Thread.Sleep(100);
            }
        }
    }
}
```

### 7.2 调试模式

```csharp
// 1. 启用调试模式
client.SetGlobalSetting("debug_mode", true);

// 2. 添加调试输出
#if DEBUG
public void DebugOutput(string message)
{
    Console.WriteLine($"[DEBUG] {DateTime.Now:HH:mm:ss.fff} {message}");
    ETLogManager.Debug(message);
}
#endif

// 3. 断点调试
public async Task DebugApiCallAsync(string endpoint)
{
    Console.WriteLine($"准备调用API: {endpoint}");
    
    var stopwatch = Stopwatch.StartNew();
    try
    {
        var result = await client.ApiClient.GetAsync<object>(endpoint);
        stopwatch.Stop();
        
        Console.WriteLine($"API调用成功，耗时: {stopwatch.ElapsedMilliseconds}ms");
        Console.WriteLine($"返回数据: {JsonConvert.SerializeObject(result, Formatting.Indented)}");
    }
    catch (Exception ex)
    {
        stopwatch.Stop();
        Console.WriteLine($"API调用失败，耗时: {stopwatch.ElapsedMilliseconds}ms");
        Console.WriteLine($"错误信息: {ex.Message}");
        Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
    }
}
```

## 8. 常见错误代码

| 错误代码 | 描述 | 解决方案 |
|---------|------|----------|
| AUTH_001 | 认证失败 | 检查用户名密码，确认账户状态 |
| AUTH_002 | 令牌过期 | 调用刷新令牌或重新登录 |
| NET_001 | 网络连接超时 | 检查网络连接，增加超时时间 |
| NET_002 | 服务器不可达 | 检查服务器地址和状态 |
| FILE_001 | 文件不存在 | 确认文件路径正确 |
| FILE_002 | 文件格式不支持 | 检查文件扩展名和格式 |
| FILE_003 | 文件大小超限 | 压缩文件或调整大小限制 |
| BROWSER_001 | 页面加载失败 | 检查URL和网络连接 |
| BROWSER_002 | 元素未找到 | 更新元素选择器，等待页面加载 |
| CONFIG_001 | 配置文件错误 | 检查配置文件格式和路径 |

## 9. 获取技术支持

如果以上解决方案无法解决您的问题，请：

1. **收集诊断信息**:
   - 启用详细日志 (`ETLogManager.SetLogLevel(LogLevel.Debug)`)
   - 记录错误发生的具体步骤
   - 收集相关的错误消息和堆栈跟踪

2. **检查系统环境**:
   - .NET Framework版本
   - 操作系统版本
   - 网络环境配置
   - OA系统版本和配置

3. **提供最小重现示例**:
   - 创建能重现问题的最小代码示例
   - 包含相关的配置信息
   - 说明预期行为和实际行为的差异

4. **查看文档**:
   - API使用文档
   - 最佳实践指南
   - 示例代码库
