# ETOAAutomation 最佳实践指南

## 概述

本指南提供了使用ETOAAutomation类库的最佳实践建议，帮助开发者编写高质量、可维护的OA自动化代码。

## 1. 初始化和配置

### 1.1 客户端初始化

```csharp
// ✅ 推荐：使用using语句确保资源正确释放
using (var client = new ETOAClient("https://your-oa-system.com"))
{
    // 配置客户端
    client.SessionManager.HeartbeatInterval = 300; // 5分钟心跳
    client.SessionManager.AutoReloginEnabled = true;
    
    // 执行操作...
}

// ❌ 不推荐：忘记释放资源
var client = new ETOAClient("https://your-oa-system.com");
// ... 操作后忘记调用 client.Dispose()
```

### 1.2 配置管理

```csharp
// ✅ 推荐：集中管理配置
public class OAConfig
{
    public static void InitializeConfig(ETOAClient client)
    {
        // 网络配置
        client.SetConfig("Network", "Timeout", "60");
        client.SetConfig("Network", "RetryCount", "3");
        
        // 上传配置
        client.SetConfig("Upload", "MaxFileSize", "10485760"); // 10MB
        client.SetConfig("Upload", "AllowedExtensions", ".pdf,.doc,.docx,.xls,.xlsx");
        
        // 全局设置
        client.SetGlobalSetting("debug_mode", false);
        client.SetGlobalSetting("log_level", "Info");
    }
}
```

## 2. 异常处理

### 2.1 分层异常处理

```csharp
// ✅ 推荐：分层处理不同类型的异常
public async Task<ApiResult<T>> SafeApiCallAsync<T>(string endpoint)
{
    try
    {
        var result = await _client.ApiClient.GetAsync<T>(endpoint);
        return ApiResult<T>.Success(result);
    }
    catch (ETException ex)
    {
        // 业务异常 - 记录并返回友好错误
        ETLogManager.Warn($"业务异常: {ex.Message}", ex);
        return ApiResult<T>.Failure(ex.Message);
    }
    catch (HttpRequestException ex)
    {
        // 网络异常 - 可能需要重试
        ETLogManager.Error($"网络异常: {ex.Message}", ex);
        return ApiResult<T>.Failure("网络连接失败，请检查网络设置");
    }
    catch (Exception ex)
    {
        // 未知异常 - 记录详细信息
        ETLogManager.Error($"未知异常: {ex.Message}", ex);
        return ApiResult<T>.Failure("系统内部错误");
    }
}
```

### 2.2 全局异常处理

```csharp
// ✅ 推荐：设置全局异常处理器
client.GlobalException += (sender, e) =>
{
    // 记录异常
    ETLogManager.Error($"全局异常 - 来源: {e.Source}", e.Exception);
    
    // 根据异常类型决定处理策略
    if (e.Exception is UnauthorizedAccessException)
    {
        // 认证失败，尝试重新登录
        _ = Task.Run(async () => await HandleReloginAsync());
    }
    
    // 标记为已处理，避免程序崩溃
    e.Handled = true;
};
```

## 3. 性能优化

### 3.1 异步操作

```csharp
// ✅ 推荐：使用异步方法，避免阻塞UI线程
public async Task ProcessDocumentsAsync(List<string> documentIds)
{
    var tasks = documentIds.Select(async id =>
    {
        try
        {
            return await _client.ApiClient.GetAsync<Document>($"/api/documents/{id}");
        }
        catch (Exception ex)
        {
            ETLogManager.Error($"处理文档 {id} 失败", ex);
            return null;
        }
    });
    
    var results = await Task.WhenAll(tasks);
    var validResults = results.Where(r => r != null).ToList();
    
    // 处理结果...
}

// ❌ 不推荐：同步调用，可能导致界面卡顿
public List<Document> ProcessDocumentsSync(List<string> documentIds)
{
    var results = new List<Document>();
    foreach (var id in documentIds)
    {
        var doc = _client.ApiClient.GetAsync<Document>($"/api/documents/{id}").Result;
        results.Add(doc);
    }
    return results;
}
```

### 3.2 批量操作

```csharp
// ✅ 推荐：使用批量API减少网络请求
public async Task<List<UploadResult>> UploadMultipleFilesAsync(List<string> filePaths)
{
    // 使用批量上传API
    return await _client.FileUploader.UploadMultipleFilesAsync("/api/upload/batch", filePaths);
}

// ❌ 不推荐：逐个上传文件
public async Task<List<UploadResult>> UploadFilesOneByOneAsync(List<string> filePaths)
{
    var results = new List<UploadResult>();
    foreach (var filePath in filePaths)
    {
        var result = await _client.FileUploader.UploadFileAsync("/api/upload", filePath);
        results.Add(result);
    }
    return results;
}
```

### 3.3 缓存策略

```csharp
// ✅ 推荐：实现智能缓存
public class CachedApiClient
{
    private readonly ETOAClient _client;
    private readonly Dictionary<string, (DateTime expiry, object data)> _cache;
    private readonly TimeSpan _defaultCacheTime = TimeSpan.FromMinutes(5);

    public async Task<T> GetWithCacheAsync<T>(string endpoint, TimeSpan? cacheTime = null)
    {
        var expiry = DateTime.Now.Add(cacheTime ?? _defaultCacheTime);
        
        if (_cache.TryGetValue(endpoint, out var cached) && cached.expiry > DateTime.Now)
        {
            return (T)cached.data;
        }
        
        var result = await _client.ApiClient.GetAsync<T>(endpoint);
        _cache[endpoint] = (expiry, result);
        
        return result;
    }
}
```

## 4. 安全性

### 4.1 凭据管理

```csharp
// ✅ 推荐：安全存储凭据
public class SecureCredentialManager
{
    public static async Task<bool> LoginWithStoredCredentialsAsync(ETOAClient client)
    {
        try
        {
            // 从安全存储中获取凭据（如Windows凭据管理器）
            var credentials = GetStoredCredentials();
            
            if (credentials != null)
            {
                return await client.LoginAsync(credentials.Username, credentials.Password);
            }
            
            // 如果没有存储的凭据，提示用户输入
            return await PromptForCredentialsAsync(client);
        }
        catch (Exception ex)
        {
            ETLogManager.Error("登录失败", ex);
            return false;
        }
    }
    
    private static SecureCredentials GetStoredCredentials()
    {
        // 实现安全的凭据获取逻辑
        // 避免在代码中硬编码用户名和密码
        return null;
    }
}

// ❌ 不推荐：在代码中硬编码凭据
var success = await client.LoginAsync("hardcoded_user", "hardcoded_password");
```

### 4.2 敏感数据处理

```csharp
// ✅ 推荐：避免在日志中记录敏感信息
public void LogApiCall(string endpoint, object requestData)
{
    var sanitizedData = SanitizeLogData(requestData);
    ETLogManager.Info($"API调用: {endpoint}, 参数: {sanitizedData}");
}

private object SanitizeLogData(object data)
{
    // 移除或掩码敏感字段
    var json = JsonConvert.SerializeObject(data);
    // 替换密码、令牌等敏感信息
    json = Regex.Replace(json, @"""password""\s*:\s*""[^""]*""", @"""password"":""***""");
    json = Regex.Replace(json, @"""token""\s*:\s*""[^""]*""", @"""token"":""***""");
    return json;
}
```

## 5. 会话管理

### 5.1 会话监控

```csharp
// ✅ 推荐：实现完整的会话监控
public class SessionMonitor
{
    private readonly ETOAClient _client;
    private Timer _statusCheckTimer;

    public SessionMonitor(ETOAClient client)
    {
        _client = client;
        InitializeMonitoring();
    }

    private void InitializeMonitoring()
    {
        // 订阅会话事件
        _client.SessionManager.SessionStatusChanged += OnSessionStatusChanged;
        _client.SessionManager.HeartbeatFailed += OnHeartbeatFailed;
        _client.SessionManager.AutoReloginAttempted += OnAutoReloginAttempted;
        
        // 定期检查会话状态
        _statusCheckTimer = new Timer(CheckSessionStatus, null, 
            TimeSpan.FromMinutes(1), TimeSpan.FromMinutes(1));
    }

    private void OnSessionStatusChanged(object sender, SessionStatusChangedEventArgs e)
    {
        ETLogManager.Info($"会话状态变更: {e.OldStatus} -> {e.NewStatus}");
        
        if (e.NewStatus == SessionStatus.Inactive)
        {
            // 会话失效，通知用户或尝试重连
            NotifySessionInactive();
        }
    }

    private void CheckSessionStatus(object state)
    {
        var stats = _client.GetSessionStatistics();
        
        if (!stats.IsActive && _client.IsLoggedIn)
        {
            ETLogManager.Warn("检测到会话异常状态");
            // 执行恢复操作
        }
    }
}
```

### 5.2 自动重连策略

```csharp
// ✅ 推荐：实现智能重连策略
public class ReconnectionManager
{
    private readonly ETOAClient _client;
    private int _reconnectAttempts = 0;
    private readonly int _maxReconnectAttempts = 5;
    private readonly TimeSpan[] _backoffIntervals = 
    {
        TimeSpan.FromSeconds(5),
        TimeSpan.FromSeconds(10),
        TimeSpan.FromSeconds(30),
        TimeSpan.FromMinutes(1),
        TimeSpan.FromMinutes(5)
    };

    public async Task<bool> AttemptReconnectionAsync()
    {
        while (_reconnectAttempts < _maxReconnectAttempts)
        {
            try
            {
                var delay = _backoffIntervals[Math.Min(_reconnectAttempts, _backoffIntervals.Length - 1)];
                await Task.Delay(delay);
                
                ETLogManager.Info($"尝试重连，第 {_reconnectAttempts + 1} 次");
                
                if (await _client.LoginAsync(_lastUsername, _lastPassword))
                {
                    _reconnectAttempts = 0;
                    ETLogManager.Info("重连成功");
                    return true;
                }
                
                _reconnectAttempts++;
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"重连失败，第 {_reconnectAttempts + 1} 次", ex);
                _reconnectAttempts++;
            }
        }
        
        ETLogManager.Error("重连失败，已达到最大尝试次数");
        return false;
    }
}
```

## 6. 文件操作

### 6.1 文件上传最佳实践

```csharp
// ✅ 推荐：实现完整的文件上传流程
public async Task<UploadResult> SafeUploadFileAsync(string filePath, string category = "document")
{
    try
    {
        // 1. 验证文件
        if (!ValidateFile(filePath))
        {
            return UploadResult.Failure("文件验证失败");
        }
        
        // 2. 检查文件大小
        var fileInfo = new FileInfo(filePath);
        var maxSize = _client.GetGlobalSetting<long>("max_file_size", 10 * 1024 * 1024); // 10MB
        
        if (fileInfo.Length > maxSize)
        {
            return UploadResult.Failure($"文件大小超过限制 ({maxSize / 1024 / 1024}MB)");
        }
        
        // 3. 显示上传进度
        var progressHandler = new Progress<UploadProgressEventArgs>(progress =>
        {
            Console.WriteLine($"上传进度: {progress.ProgressPercentage}%");
        });
        
        // 4. 执行上传
        var result = await _client.FileUploader.UploadFileAsync(
            "/api/upload", filePath, category, progressHandler);
        
        // 5. 记录结果
        if (result.Success)
        {
            ETLogManager.Info($"文件上传成功: {filePath} -> {result.FileId}");
        }
        else
        {
            ETLogManager.Error($"文件上传失败: {filePath} - {result.ErrorMessage}");
        }
        
        return result;
    }
    catch (Exception ex)
    {
        ETLogManager.Error($"文件上传异常: {filePath}", ex);
        return UploadResult.Failure($"上传异常: {ex.Message}");
    }
}

private bool ValidateFile(string filePath)
{
    if (!File.Exists(filePath))
    {
        ETLogManager.Error($"文件不存在: {filePath}");
        return false;
    }
    
    var allowedExtensions = _client.GetConfig("Upload", "AllowedExtensions", ".pdf,.doc,.docx")
        .Split(',').Select(ext => ext.Trim().ToLower()).ToArray();
    
    var fileExtension = Path.GetExtension(filePath).ToLower();
    
    if (!allowedExtensions.Contains(fileExtension))
    {
        ETLogManager.Error($"不支持的文件类型: {fileExtension}");
        return false;
    }
    
    return true;
}
```

## 7. 浏览器操作

### 7.1 稳定的页面操作

```csharp
// ✅ 推荐：实现稳定的页面操作
public async Task<bool> SafePageOperationAsync(ETOASimulationBrowser browser, string selector, string value)
{
    const int maxRetries = 3;
    const int waitTimeMs = 1000;
    
    for (int i = 0; i < maxRetries; i++)
    {
        try
        {
            // 等待元素出现
            await browser.WaitForElementAsync(selector, TimeSpan.FromSeconds(10));
            
            // 检查元素是否可见和可操作
            if (await browser.IsElementVisibleAsync(selector) && 
                await browser.IsElementEnabledAsync(selector))
            {
                await browser.SetElementValueAsync(selector, value);
                return true;
            }
            
            // 等待后重试
            await Task.Delay(waitTimeMs);
        }
        catch (Exception ex)
        {
            ETLogManager.Warn($"页面操作失败，第 {i + 1} 次尝试: {ex.Message}");
            
            if (i == maxRetries - 1)
            {
                ETLogManager.Error($"页面操作最终失败: {selector}", ex);
                return false;
            }
            
            await Task.Delay(waitTimeMs * (i + 1)); // 递增等待时间
        }
    }
    
    return false;
}
```

## 8. 日志和监控

### 8.1 结构化日志

```csharp
// ✅ 推荐：使用结构化日志
public class StructuredLogger
{
    public static void LogApiCall(string method, string endpoint, TimeSpan duration, bool success)
    {
        var logData = new
        {
            Type = "ApiCall",
            Method = method,
            Endpoint = endpoint,
            Duration = duration.TotalMilliseconds,
            Success = success,
            Timestamp = DateTime.UtcNow
        };
        
        ETLogManager.Info($"API调用: {JsonConvert.SerializeObject(logData)}");
    }
    
    public static void LogUserAction(string action, string details, string userId = null)
    {
        var logData = new
        {
            Type = "UserAction",
            Action = action,
            Details = details,
            UserId = userId ?? "Unknown",
            Timestamp = DateTime.UtcNow
        };
        
        ETLogManager.Info($"用户操作: {JsonConvert.SerializeObject(logData)}");
    }
}
```

### 8.2 性能监控

```csharp
// ✅ 推荐：实现性能监控
public class PerformanceMonitor
{
    private readonly ETOAClient _client;
    private readonly Timer _monitorTimer;

    public PerformanceMonitor(ETOAClient client)
    {
        _client = client;
        _monitorTimer = new Timer(CheckPerformance, null, 
            TimeSpan.FromMinutes(1), TimeSpan.FromMinutes(1));
    }

    private void CheckPerformance(object state)
    {
        try
        {
            var stats = _client.GetPerformanceStatistics();
            
            // 检查CPU使用率
            if (stats.CpuUsage > 80)
            {
                ETLogManager.Warn($"CPU使用率过高: {stats.CpuUsage}%");
            }
            
            // 检查内存使用
            if (stats.MemoryUsage > 500) // 500MB
            {
                ETLogManager.Warn($"内存使用过高: {stats.MemoryUsage}MB");
            }
            
            // 检查网络延迟
            if (stats.NetworkLatency > 5000) // 5秒
            {
                ETLogManager.Warn($"网络延迟过高: {stats.NetworkLatency}ms");
            }
        }
        catch (Exception ex)
        {
            ETLogManager.Error("性能监控失败", ex);
        }
    }
}
```

## 9. 测试策略

### 9.1 单元测试

```csharp
// ✅ 推荐：编写全面的单元测试
[TestClass]
public class ETOAClientTests
{
    private ETOAClient _client;
    private Mock<IApiClient> _mockApiClient;

    [TestInitialize]
    public void Setup()
    {
        _mockApiClient = new Mock<IApiClient>();
        _client = new ETOAClient("https://test-oa-system.com");
        // 注入模拟对象...
    }

    [TestMethod]
    public async Task LoginAsync_ValidCredentials_ReturnsTrue()
    {
        // Arrange
        var expectedLoginInfo = new ETOALoginInfo 
        { 
            IsSuccess = true, 
            UserId = "test_user",
            Token = "test_token"
        };
        
        _mockApiClient.Setup(x => x.PostAsync<ETOALoginInfo>(It.IsAny<string>(), It.IsAny<object>()))
                     .ReturnsAsync(expectedLoginInfo);

        // Act
        var result = await _client.LoginAsync("test_user", "test_password");

        // Assert
        Assert.IsTrue(result);
        Assert.IsTrue(_client.IsLoggedIn);
        Assert.AreEqual("test_user", _client.LoginInfo.UserId);
    }

    [TestCleanup]
    public void Cleanup()
    {
        _client?.Dispose();
    }
}
```

## 10. 部署和维护

### 10.1 配置文件管理

```ini
# ETOAAutomation.ini 示例配置文件
[Server]
Url=https://your-oa-system.com
Timeout=60
RetryCount=3

[Upload]
MaxFileSize=10485760
AllowedExtensions=.pdf,.doc,.docx,.xls,.xlsx
TempDirectory=C:\Temp\ETOAUploads

[Session]
HeartbeatInterval=300
AutoReloginEnabled=true
MaxReloginAttempts=3

[Logging]
Level=Info
MaxFileSize=10485760
MaxFiles=10
```

### 10.2 健康检查端点

```csharp
// ✅ 推荐：实现健康检查
public class HealthCheckService
{
    private readonly ETOAClient _client;

    public async Task<HealthStatus> GetHealthStatusAsync()
    {
        var status = new HealthStatus();
        
        try
        {
            // 检查基本连接
            status.IsConnected = await CheckConnectionAsync();
            
            // 检查认证状态
            status.IsAuthenticated = _client.IsLoggedIn;
            
            // 检查会话状态
            var sessionStats = _client.GetSessionStatistics();
            status.SessionHealth = sessionStats.IsActive;
            
            // 检查性能指标
            var perfStats = _client.GetPerformanceStatistics();
            status.PerformanceHealth = perfStats.IsHealthy;
            
            status.OverallHealth = status.IsConnected && 
                                 status.IsAuthenticated && 
                                 status.SessionHealth && 
                                 status.PerformanceHealth;
            
            status.LastChecked = DateTime.UtcNow;
        }
        catch (Exception ex)
        {
            status.OverallHealth = false;
            status.ErrorMessage = ex.Message;
        }
        
        return status;
    }
}
```

## 总结

遵循这些最佳实践可以帮助您：

1. **提高代码质量** - 通过正确的异常处理和资源管理
2. **增强系统稳定性** - 通过会话监控和自动重连
3. **优化性能** - 通过异步操作和缓存策略
4. **确保安全性** - 通过安全的凭据管理和敏感数据处理
5. **简化维护** - 通过结构化日志和健康检查
6. **提升用户体验** - 通过稳定的操作和友好的错误处理

记住，最佳实践是不断演进的，请根据您的具体需求和环境进行调整。
