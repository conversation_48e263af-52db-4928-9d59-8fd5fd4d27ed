namespace ET.ETOAAutomation
{
    partial class ETOAUploadConfigForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.TabControlMain = new System.Windows.Forms.TabControl();
            this.TabPageBasic = new System.Windows.Forms.TabPage();
            this.GroupBoxBasic = new System.Windows.Forms.GroupBox();
            this.NumTimeout = new System.Windows.Forms.NumericUpDown();
            this.LblTimeout = new System.Windows.Forms.Label();
            this.NumRetryCount = new System.Windows.Forms.NumericUpDown();
            this.LblRetryCount = new System.Windows.Forms.Label();
            this.NumMaxConcurrent = new System.Windows.Forms.NumericUpDown();
            this.LblMaxConcurrent = new System.Windows.Forms.Label();
            this.NumChunkSize = new System.Windows.Forms.NumericUpDown();
            this.LblChunkSize = new System.Windows.Forms.Label();
            this.NumMaxFileSize = new System.Windows.Forms.NumericUpDown();
            this.LblMaxFileSize = new System.Windows.Forms.Label();
            this.TabPageAdvanced = new System.Windows.Forms.TabPage();
            this.GroupBoxAdvanced = new System.Windows.Forms.GroupBox();
            this.NumProgressInterval = new System.Windows.Forms.NumericUpDown();
            this.LblProgressInterval = new System.Windows.Forms.Label();
            this.ChkAutoRetry = new System.Windows.Forms.CheckBox();
            this.ChkIntegrityCheck = new System.Windows.Forms.CheckBox();
            this.ChkResumableUpload = new System.Windows.Forms.CheckBox();
            this.TabPageExtensions = new System.Windows.Forms.TabPage();
            this.GroupBoxExtensions = new System.Windows.Forms.GroupBox();
            this.PanelExtensionButtons = new System.Windows.Forms.Panel();
            this.BtnRemoveExtension = new System.Windows.Forms.Button();
            this.BtnAddExtension = new System.Windows.Forms.Button();
            this.TxtNewExtension = new System.Windows.Forms.TextBox();
            this.LblNewExtension = new System.Windows.Forms.Label();
            this.LstExtensions = new System.Windows.Forms.ListBox();
            this.LblExtensions = new System.Windows.Forms.Label();
            this.PanelButtons = new System.Windows.Forms.Panel();
            this.BtnReset = new System.Windows.Forms.Button();
            this.BtnCancel = new System.Windows.Forms.Button();
            this.BtnSave = new System.Windows.Forms.Button();
            this.TabControlMain.SuspendLayout();
            this.TabPageBasic.SuspendLayout();
            this.GroupBoxBasic.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.NumTimeout)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.NumRetryCount)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.NumMaxConcurrent)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.NumChunkSize)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.NumMaxFileSize)).BeginInit();
            this.TabPageAdvanced.SuspendLayout();
            this.GroupBoxAdvanced.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.NumProgressInterval)).BeginInit();
            this.TabPageExtensions.SuspendLayout();
            this.GroupBoxExtensions.SuspendLayout();
            this.PanelExtensionButtons.SuspendLayout();
            this.PanelButtons.SuspendLayout();
            this.SuspendLayout();
            // 
            // TabControlMain
            // 
            this.TabControlMain.Controls.Add(this.TabPageBasic);
            this.TabControlMain.Controls.Add(this.TabPageAdvanced);
            this.TabControlMain.Controls.Add(this.TabPageExtensions);
            this.TabControlMain.Dock = System.Windows.Forms.DockStyle.Fill;
            this.TabControlMain.Location = new System.Drawing.Point(0, 0);
            this.TabControlMain.Name = "TabControlMain";
            this.TabControlMain.SelectedIndex = 0;
            this.TabControlMain.Size = new System.Drawing.Size(484, 361);
            this.TabControlMain.TabIndex = 0;
            // 
            // TabPageBasic
            // 
            this.TabPageBasic.Controls.Add(this.GroupBoxBasic);
            this.TabPageBasic.Location = new System.Drawing.Point(4, 22);
            this.TabPageBasic.Name = "TabPageBasic";
            this.TabPageBasic.Padding = new System.Windows.Forms.Padding(10);
            this.TabPageBasic.Size = new System.Drawing.Size(476, 335);
            this.TabPageBasic.TabIndex = 0;
            this.TabPageBasic.Text = "基础配置";
            this.TabPageBasic.UseVisualStyleBackColor = true;
            // 
            // GroupBoxBasic
            // 
            this.GroupBoxBasic.Controls.Add(this.NumTimeout);
            this.GroupBoxBasic.Controls.Add(this.LblTimeout);
            this.GroupBoxBasic.Controls.Add(this.NumRetryCount);
            this.GroupBoxBasic.Controls.Add(this.LblRetryCount);
            this.GroupBoxBasic.Controls.Add(this.NumMaxConcurrent);
            this.GroupBoxBasic.Controls.Add(this.LblMaxConcurrent);
            this.GroupBoxBasic.Controls.Add(this.NumChunkSize);
            this.GroupBoxBasic.Controls.Add(this.LblChunkSize);
            this.GroupBoxBasic.Controls.Add(this.NumMaxFileSize);
            this.GroupBoxBasic.Controls.Add(this.LblMaxFileSize);
            this.GroupBoxBasic.Dock = System.Windows.Forms.DockStyle.Fill;
            this.GroupBoxBasic.Location = new System.Drawing.Point(10, 10);
            this.GroupBoxBasic.Name = "GroupBoxBasic";
            this.GroupBoxBasic.Padding = new System.Windows.Forms.Padding(10);
            this.GroupBoxBasic.Size = new System.Drawing.Size(456, 315);
            this.GroupBoxBasic.TabIndex = 0;
            this.GroupBoxBasic.TabStop = false;
            this.GroupBoxBasic.Text = "基础参数";
            // 
            // NumTimeout
            // 
            this.NumTimeout.Location = new System.Drawing.Point(150, 140);
            this.NumTimeout.Maximum = new decimal(new int[] {
            3600,
            0,
            0,
            0});
            this.NumTimeout.Minimum = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.NumTimeout.Name = "NumTimeout";
            this.NumTimeout.Size = new System.Drawing.Size(120, 21);
            this.NumTimeout.TabIndex = 9;
            this.NumTimeout.Value = new decimal(new int[] {
            300,
            0,
            0,
            0});
            // 
            // LblTimeout
            // 
            this.LblTimeout.AutoSize = true;
            this.LblTimeout.Location = new System.Drawing.Point(20, 142);
            this.LblTimeout.Name = "LblTimeout";
            this.LblTimeout.Size = new System.Drawing.Size(89, 12);
            this.LblTimeout.TabIndex = 8;
            this.LblTimeout.Text = "超时时间(秒):";
            // 
            // NumRetryCount
            // 
            this.NumRetryCount.Location = new System.Drawing.Point(150, 110);
            this.NumRetryCount.Maximum = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.NumRetryCount.Name = "NumRetryCount";
            this.NumRetryCount.Size = new System.Drawing.Size(120, 21);
            this.NumRetryCount.TabIndex = 7;
            this.NumRetryCount.Value = new decimal(new int[] {
            3,
            0,
            0,
            0});
            // 
            // LblRetryCount
            // 
            this.LblRetryCount.AutoSize = true;
            this.LblRetryCount.Location = new System.Drawing.Point(20, 112);
            this.LblRetryCount.Name = "LblRetryCount";
            this.LblRetryCount.Size = new System.Drawing.Size(65, 12);
            this.LblRetryCount.TabIndex = 6;
            this.LblRetryCount.Text = "重试次数:";
            // 
            // NumMaxConcurrent
            // 
            this.NumMaxConcurrent.Location = new System.Drawing.Point(150, 80);
            this.NumMaxConcurrent.Maximum = new decimal(new int[] {
            20,
            0,
            0,
            0});
            this.NumMaxConcurrent.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.NumMaxConcurrent.Name = "NumMaxConcurrent";
            this.NumMaxConcurrent.Size = new System.Drawing.Size(120, 21);
            this.NumMaxConcurrent.TabIndex = 5;
            this.NumMaxConcurrent.Value = new decimal(new int[] {
            3,
            0,
            0,
            0});
            // 
            // LblMaxConcurrent
            // 
            this.LblMaxConcurrent.AutoSize = true;
            this.LblMaxConcurrent.Location = new System.Drawing.Point(20, 82);
            this.LblMaxConcurrent.Name = "LblMaxConcurrent";
            this.LblMaxConcurrent.Size = new System.Drawing.Size(89, 12);
            this.LblMaxConcurrent.TabIndex = 4;
            this.LblMaxConcurrent.Text = "最大并发数:";
            // 
            // NumChunkSize
            // 
            this.NumChunkSize.Location = new System.Drawing.Point(150, 50);
            this.NumChunkSize.Maximum = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.NumChunkSize.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.NumChunkSize.Name = "NumChunkSize";
            this.NumChunkSize.Size = new System.Drawing.Size(120, 21);
            this.NumChunkSize.TabIndex = 3;
            this.NumChunkSize.Value = new decimal(new int[] {
            1,
            0,
            0,
            0});
            // 
            // LblChunkSize
            // 
            this.LblChunkSize.AutoSize = true;
            this.LblChunkSize.Location = new System.Drawing.Point(20, 52);
            this.LblChunkSize.Name = "LblChunkSize";
            this.LblChunkSize.Size = new System.Drawing.Size(89, 12);
            this.LblChunkSize.TabIndex = 2;
            this.LblChunkSize.Text = "分块大小(MB):";
            // 
            // NumMaxFileSize
            // 
            this.NumMaxFileSize.Location = new System.Drawing.Point(150, 20);
            this.NumMaxFileSize.Maximum = new decimal(new int[] {
            10240,
            0,
            0,
            0});
            this.NumMaxFileSize.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.NumMaxFileSize.Name = "NumMaxFileSize";
            this.NumMaxFileSize.Size = new System.Drawing.Size(120, 21);
            this.NumMaxFileSize.TabIndex = 1;
            this.NumMaxFileSize.Value = new decimal(new int[] {
            100,
            0,
            0,
            0});
            // 
            // LblMaxFileSize
            // 
            this.LblMaxFileSize.AutoSize = true;
            this.LblMaxFileSize.Location = new System.Drawing.Point(20, 22);
            this.LblMaxFileSize.Name = "LblMaxFileSize";
            this.LblMaxFileSize.Size = new System.Drawing.Size(113, 12);
            this.LblMaxFileSize.TabIndex = 0;
            this.LblMaxFileSize.Text = "最大文件大小(MB):";
            // 
            // TabPageAdvanced
            // 
            this.TabPageAdvanced.Controls.Add(this.GroupBoxAdvanced);
            this.TabPageAdvanced.Location = new System.Drawing.Point(4, 22);
            this.TabPageAdvanced.Name = "TabPageAdvanced";
            this.TabPageAdvanced.Padding = new System.Windows.Forms.Padding(10);
            this.TabPageAdvanced.Size = new System.Drawing.Size(476, 335);
            this.TabPageAdvanced.TabIndex = 1;
            this.TabPageAdvanced.Text = "高级配置";
            this.TabPageAdvanced.UseVisualStyleBackColor = true;
            // 
            // GroupBoxAdvanced
            // 
            this.GroupBoxAdvanced.Controls.Add(this.NumProgressInterval);
            this.GroupBoxAdvanced.Controls.Add(this.LblProgressInterval);
            this.GroupBoxAdvanced.Controls.Add(this.ChkAutoRetry);
            this.GroupBoxAdvanced.Controls.Add(this.ChkIntegrityCheck);
            this.GroupBoxAdvanced.Controls.Add(this.ChkResumableUpload);
            this.GroupBoxAdvanced.Dock = System.Windows.Forms.DockStyle.Fill;
            this.GroupBoxAdvanced.Location = new System.Drawing.Point(10, 10);
            this.GroupBoxAdvanced.Name = "GroupBoxAdvanced";
            this.GroupBoxAdvanced.Padding = new System.Windows.Forms.Padding(10);
            this.GroupBoxAdvanced.Size = new System.Drawing.Size(456, 315);
            this.GroupBoxAdvanced.TabIndex = 0;
            this.GroupBoxAdvanced.TabStop = false;
            this.GroupBoxAdvanced.Text = "高级功能";
            // 
            // NumProgressInterval
            // 
            this.NumProgressInterval.Location = new System.Drawing.Point(180, 110);
            this.NumProgressInterval.Maximum = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.NumProgressInterval.Minimum = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.NumProgressInterval.Name = "NumProgressInterval";
            this.NumProgressInterval.Size = new System.Drawing.Size(120, 21);
            this.NumProgressInterval.TabIndex = 4;
            this.NumProgressInterval.Value = new decimal(new int[] {
            1000,
            0,
            0,
            0});
            // 
            // LblProgressInterval
            // 
            this.LblProgressInterval.AutoSize = true;
            this.LblProgressInterval.Location = new System.Drawing.Point(20, 112);
            this.LblProgressInterval.Name = "LblProgressInterval";
            this.LblProgressInterval.Size = new System.Drawing.Size(125, 12);
            this.LblProgressInterval.TabIndex = 3;
            this.LblProgressInterval.Text = "进度更新间隔(毫秒):";
            // 
            // ChkAutoRetry
            // 
            this.ChkAutoRetry.AutoSize = true;
            this.ChkAutoRetry.Location = new System.Drawing.Point(20, 80);
            this.ChkAutoRetry.Name = "ChkAutoRetry";
            this.ChkAutoRetry.Size = new System.Drawing.Size(84, 16);
            this.ChkAutoRetry.TabIndex = 2;
            this.ChkAutoRetry.Text = "启用自动重试";
            this.ChkAutoRetry.UseVisualStyleBackColor = true;
            // 
            // ChkIntegrityCheck
            // 
            this.ChkIntegrityCheck.AutoSize = true;
            this.ChkIntegrityCheck.Location = new System.Drawing.Point(20, 50);
            this.ChkIntegrityCheck.Name = "ChkIntegrityCheck";
            this.ChkIntegrityCheck.Size = new System.Drawing.Size(108, 16);
            this.ChkIntegrityCheck.TabIndex = 1;
            this.ChkIntegrityCheck.Text = "启用完整性校验";
            this.ChkIntegrityCheck.UseVisualStyleBackColor = true;
            // 
            // ChkResumableUpload
            // 
            this.ChkResumableUpload.AutoSize = true;
            this.ChkResumableUpload.Location = new System.Drawing.Point(20, 20);
            this.ChkResumableUpload.Name = "ChkResumableUpload";
            this.ChkResumableUpload.Size = new System.Drawing.Size(84, 16);
            this.ChkResumableUpload.TabIndex = 0;
            this.ChkResumableUpload.Text = "启用断点续传";
            this.ChkResumableUpload.UseVisualStyleBackColor = true;
            // 
            // TabPageExtensions
            // 
            this.TabPageExtensions.Controls.Add(this.GroupBoxExtensions);
            this.TabPageExtensions.Location = new System.Drawing.Point(4, 22);
            this.TabPageExtensions.Name = "TabPageExtensions";
            this.TabPageExtensions.Padding = new System.Windows.Forms.Padding(10);
            this.TabPageExtensions.Size = new System.Drawing.Size(476, 335);
            this.TabPageExtensions.TabIndex = 2;
            this.TabPageExtensions.Text = "文件类型";
            this.TabPageExtensions.UseVisualStyleBackColor = true;
            // 
            // GroupBoxExtensions
            // 
            this.GroupBoxExtensions.Controls.Add(this.PanelExtensionButtons);
            this.GroupBoxExtensions.Controls.Add(this.LstExtensions);
            this.GroupBoxExtensions.Controls.Add(this.LblExtensions);
            this.GroupBoxExtensions.Dock = System.Windows.Forms.DockStyle.Fill;
            this.GroupBoxExtensions.Location = new System.Drawing.Point(10, 10);
            this.GroupBoxExtensions.Name = "GroupBoxExtensions";
            this.GroupBoxExtensions.Padding = new System.Windows.Forms.Padding(10);
            this.GroupBoxExtensions.Size = new System.Drawing.Size(456, 315);
            this.GroupBoxExtensions.TabIndex = 0;
            this.GroupBoxExtensions.TabStop = false;
            this.GroupBoxExtensions.Text = "允许的文件扩展名";
            // 
            // PanelExtensionButtons
            // 
            this.PanelExtensionButtons.Controls.Add(this.BtnRemoveExtension);
            this.PanelExtensionButtons.Controls.Add(this.BtnAddExtension);
            this.PanelExtensionButtons.Controls.Add(this.TxtNewExtension);
            this.PanelExtensionButtons.Controls.Add(this.LblNewExtension);
            this.PanelExtensionButtons.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.PanelExtensionButtons.Location = new System.Drawing.Point(10, 255);
            this.PanelExtensionButtons.Name = "PanelExtensionButtons";
            this.PanelExtensionButtons.Size = new System.Drawing.Size(436, 50);
            this.PanelExtensionButtons.TabIndex = 2;
            // 
            // BtnRemoveExtension
            // 
            this.BtnRemoveExtension.Location = new System.Drawing.Point(280, 15);
            this.BtnRemoveExtension.Name = "BtnRemoveExtension";
            this.BtnRemoveExtension.Size = new System.Drawing.Size(75, 23);
            this.BtnRemoveExtension.TabIndex = 3;
            this.BtnRemoveExtension.Text = "移除";
            this.BtnRemoveExtension.UseVisualStyleBackColor = true;
            // 
            // BtnAddExtension
            // 
            this.BtnAddExtension.Location = new System.Drawing.Point(200, 15);
            this.BtnAddExtension.Name = "BtnAddExtension";
            this.BtnAddExtension.Size = new System.Drawing.Size(75, 23);
            this.BtnAddExtension.TabIndex = 2;
            this.BtnAddExtension.Text = "添加";
            this.BtnAddExtension.UseVisualStyleBackColor = true;
            // 
            // TxtNewExtension
            // 
            this.TxtNewExtension.Location = new System.Drawing.Point(80, 17);
            this.TxtNewExtension.Name = "TxtNewExtension";
            this.TxtNewExtension.Size = new System.Drawing.Size(100, 21);
            this.TxtNewExtension.TabIndex = 1;
            // 
            // LblNewExtension
            // 
            this.LblNewExtension.AutoSize = true;
            this.LblNewExtension.Location = new System.Drawing.Point(10, 20);
            this.LblNewExtension.Name = "LblNewExtension";
            this.LblNewExtension.Size = new System.Drawing.Size(65, 12);
            this.LblNewExtension.TabIndex = 0;
            this.LblNewExtension.Text = "新扩展名:";
            // 
            // LstExtensions
            // 
            this.LstExtensions.Dock = System.Windows.Forms.DockStyle.Fill;
            this.LstExtensions.FormattingEnabled = true;
            this.LstExtensions.ItemHeight = 12;
            this.LstExtensions.Location = new System.Drawing.Point(10, 35);
            this.LstExtensions.Name = "LstExtensions";
            this.LstExtensions.Size = new System.Drawing.Size(436, 220);
            this.LstExtensions.TabIndex = 1;
            // 
            // LblExtensions
            // 
            this.LblExtensions.AutoSize = true;
            this.LblExtensions.Dock = System.Windows.Forms.DockStyle.Top;
            this.LblExtensions.Location = new System.Drawing.Point(10, 23);
            this.LblExtensions.Name = "LblExtensions";
            this.LblExtensions.Size = new System.Drawing.Size(125, 12);
            this.LblExtensions.TabIndex = 0;
            this.LblExtensions.Text = "当前允许的文件类型:";
            // 
            // PanelButtons
            // 
            this.PanelButtons.Controls.Add(this.BtnReset);
            this.PanelButtons.Controls.Add(this.BtnCancel);
            this.PanelButtons.Controls.Add(this.BtnSave);
            this.PanelButtons.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.PanelButtons.Location = new System.Drawing.Point(0, 361);
            this.PanelButtons.Name = "PanelButtons";
            this.PanelButtons.Padding = new System.Windows.Forms.Padding(10);
            this.PanelButtons.Size = new System.Drawing.Size(484, 50);
            this.PanelButtons.TabIndex = 1;
            // 
            // BtnReset
            // 
            this.BtnReset.Location = new System.Drawing.Point(10, 15);
            this.BtnReset.Name = "BtnReset";
            this.BtnReset.Size = new System.Drawing.Size(75, 25);
            this.BtnReset.TabIndex = 2;
            this.BtnReset.Text = "重置";
            this.BtnReset.UseVisualStyleBackColor = true;
            // 
            // BtnCancel
            // 
            this.BtnCancel.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.BtnCancel.Location = new System.Drawing.Point(399, 15);
            this.BtnCancel.Name = "BtnCancel";
            this.BtnCancel.Size = new System.Drawing.Size(75, 25);
            this.BtnCancel.TabIndex = 1;
            this.BtnCancel.Text = "取消";
            this.BtnCancel.UseVisualStyleBackColor = true;
            // 
            // BtnSave
            // 
            this.BtnSave.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.BtnSave.Location = new System.Drawing.Point(318, 15);
            this.BtnSave.Name = "BtnSave";
            this.BtnSave.Size = new System.Drawing.Size(75, 25);
            this.BtnSave.TabIndex = 0;
            this.BtnSave.Text = "保存";
            this.BtnSave.UseVisualStyleBackColor = true;
            // 
            // ETOAUploadConfigForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(484, 411);
            this.Controls.Add(this.TabControlMain);
            this.Controls.Add(this.PanelButtons);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "ETOAUploadConfigForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "文件上传配置";
            this.TabControlMain.ResumeLayout(false);
            this.TabPageBasic.ResumeLayout(false);
            this.GroupBoxBasic.ResumeLayout(false);
            this.GroupBoxBasic.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.NumTimeout)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.NumRetryCount)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.NumMaxConcurrent)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.NumChunkSize)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.NumMaxFileSize)).EndInit();
            this.TabPageAdvanced.ResumeLayout(false);
            this.GroupBoxAdvanced.ResumeLayout(false);
            this.GroupBoxAdvanced.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.NumProgressInterval)).EndInit();
            this.TabPageExtensions.ResumeLayout(false);
            this.GroupBoxExtensions.ResumeLayout(false);
            this.GroupBoxExtensions.PerformLayout();
            this.PanelExtensionButtons.ResumeLayout(false);
            this.PanelExtensionButtons.PerformLayout();
            this.PanelButtons.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.TabControl TabControlMain;
        private System.Windows.Forms.TabPage TabPageBasic;
        private System.Windows.Forms.GroupBox GroupBoxBasic;
        private System.Windows.Forms.NumericUpDown NumMaxFileSize;
        private System.Windows.Forms.Label LblMaxFileSize;
        private System.Windows.Forms.NumericUpDown NumTimeout;
        private System.Windows.Forms.Label LblTimeout;
        private System.Windows.Forms.NumericUpDown NumRetryCount;
        private System.Windows.Forms.Label LblRetryCount;
        private System.Windows.Forms.NumericUpDown NumMaxConcurrent;
        private System.Windows.Forms.Label LblMaxConcurrent;
        private System.Windows.Forms.NumericUpDown NumChunkSize;
        private System.Windows.Forms.Label LblChunkSize;
        private System.Windows.Forms.TabPage TabPageAdvanced;
        private System.Windows.Forms.GroupBox GroupBoxAdvanced;
        private System.Windows.Forms.CheckBox ChkResumableUpload;
        private System.Windows.Forms.NumericUpDown NumProgressInterval;
        private System.Windows.Forms.Label LblProgressInterval;
        private System.Windows.Forms.CheckBox ChkAutoRetry;
        private System.Windows.Forms.CheckBox ChkIntegrityCheck;
        private System.Windows.Forms.TabPage TabPageExtensions;
        private System.Windows.Forms.GroupBox GroupBoxExtensions;
        private System.Windows.Forms.ListBox LstExtensions;
        private System.Windows.Forms.Label LblExtensions;
        private System.Windows.Forms.Panel PanelExtensionButtons;
        private System.Windows.Forms.Button BtnRemoveExtension;
        private System.Windows.Forms.Button BtnAddExtension;
        private System.Windows.Forms.TextBox TxtNewExtension;
        private System.Windows.Forms.Label LblNewExtension;
        private System.Windows.Forms.Panel PanelButtons;
        private System.Windows.Forms.Button BtnReset;
        private System.Windows.Forms.Button BtnCancel;
        private System.Windows.Forms.Button BtnSave;
    }
}
