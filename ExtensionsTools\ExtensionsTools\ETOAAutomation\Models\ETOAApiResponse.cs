using System;
using System.Collections.Generic;
using System.Net;

namespace ET.ETOAAutomation.Models
{
    /// <summary>
    /// API响应模型
    /// </summary>
    public class ETOAApiResponse
    {
        /// <summary>
        /// 响应ID（对应请求ID）
        /// </summary>
        public string ResponseId { get; set; }

        /// <summary>
        /// 对应的请求ID
        /// </summary>
        public string RequestId { get; set; }

        /// <summary>
        /// HTTP状态码
        /// </summary>
        public HttpStatusCode StatusCode { get; set; }

        /// <summary>
        /// 状态码数值
        /// </summary>
        public int StatusCodeValue => (int)StatusCode;

        /// <summary>
        /// 响应是否成功
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// 响应头信息
        /// </summary>
        public Dictionary<string, string> Headers { get; set; }

        /// <summary>
        /// 响应内容类型
        /// </summary>
        public string ContentType { get; set; }

        /// <summary>
        /// 响应内容长度
        /// </summary>
        public long ContentLength { get; set; }

        /// <summary>
        /// 原始响应内容（字符串）
        /// </summary>
        public string RawContent { get; set; }

        /// <summary>
        /// 响应数据（反序列化后的对象）
        /// </summary>
        public object Data { get; set; }

        /// <summary>
        /// 响应数据的JSON字符串
        /// </summary>
        public string DataJson { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 异常信息
        /// </summary>
        public Exception Exception { get; set; }

        /// <summary>
        /// 请求发送时间
        /// </summary>
        public DateTime RequestTime { get; set; }

        /// <summary>
        /// 响应接收时间
        /// </summary>
        public DateTime ResponseTime { get; set; }

        /// <summary>
        /// 请求耗时（毫秒）
        /// </summary>
        public long ElapsedMilliseconds => (long)(ResponseTime - RequestTime).TotalMilliseconds;

        /// <summary>
        /// 服务器信息
        /// </summary>
        public string Server { get; set; }

        /// <summary>
        /// Cookie信息
        /// </summary>
        public Dictionary<string, string> Cookies { get; set; }

        /// <summary>
        /// 重试次数
        /// </summary>
        public int RetryCount { get; set; }

        /// <summary>
        /// 扩展属性
        /// </summary>
        public Dictionary<string, object> ExtendedProperties { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public ETOAApiResponse()
        {
            ResponseId = Guid.NewGuid().ToString();
            Headers = new Dictionary<string, string>();
            Cookies = new Dictionary<string, string>();
            ExtendedProperties = new Dictionary<string, object>();
            ResponseTime = DateTime.Now;
            RetryCount = 0;
        }

        /// <summary>
        /// 构造函数（指定请求ID）
        /// </summary>
        /// <param name="requestId">请求ID</param>
        public ETOAApiResponse(string requestId) : this()
        {
            RequestId = requestId;
        }

        /// <summary>
        /// 获取强类型的响应数据
        /// </summary>
        /// <typeparam name="T">数据类型</typeparam>
        /// <returns>强类型数据</returns>
        public T GetData<T>()
        {
            if (Data is T directCast)
            {
                return directCast;
            }

            if (Data != null)
            {
                try
                {
                    return (T)Convert.ChangeType(Data, typeof(T));
                }
                catch
                {
                    // 转换失败，返回默认值
                }
            }

            return default(T);
        }

        /// <summary>
        /// 设置响应数据
        /// </summary>
        /// <param name="data">响应数据</param>
        public void SetData(object data)
        {
            Data = data;
            
            if (data != null)
            {
                try
                {
                    DataJson = Newtonsoft.Json.JsonConvert.SerializeObject(data);
                }
                catch
                {
                    DataJson = data.ToString();
                }
            }
        }

        /// <summary>
        /// 添加响应头
        /// </summary>
        /// <param name="name">响应头名称</param>
        /// <param name="value">响应头值</param>
        public void AddHeader(string name, string value)
        {
            if (Headers.ContainsKey(name))
            {
                Headers[name] = value;
            }
            else
            {
                Headers.Add(name, value);
            }
        }

        /// <summary>
        /// 获取响应头值
        /// </summary>
        /// <param name="name">响应头名称</param>
        /// <returns>响应头值</returns>
        public string GetHeader(string name)
        {
            return Headers.ContainsKey(name) ? Headers[name] : null;
        }

        /// <summary>
        /// 添加Cookie
        /// </summary>
        /// <param name="name">Cookie名称</param>
        /// <param name="value">Cookie值</param>
        public void AddCookie(string name, string value)
        {
            if (Cookies.ContainsKey(name))
            {
                Cookies[name] = value;
            }
            else
            {
                Cookies.Add(name, value);
            }
        }

        /// <summary>
        /// 获取Cookie值
        /// </summary>
        /// <param name="name">Cookie名称</param>
        /// <returns>Cookie值</returns>
        public string GetCookie(string name)
        {
            return Cookies.ContainsKey(name) ? Cookies[name] : null;
        }

        /// <summary>
        /// 检查响应是否为成功状态
        /// </summary>
        /// <returns>是否成功</returns>
        public bool IsSuccessStatusCode()
        {
            return StatusCodeValue >= 200 && StatusCodeValue < 300;
        }

        /// <summary>
        /// 检查响应是否为客户端错误
        /// </summary>
        /// <returns>是否为客户端错误</returns>
        public bool IsClientError()
        {
            return StatusCodeValue >= 400 && StatusCodeValue < 500;
        }

        /// <summary>
        /// 检查响应是否为服务器错误
        /// </summary>
        /// <returns>是否为服务器错误</returns>
        public bool IsServerError()
        {
            return StatusCodeValue >= 500 && StatusCodeValue < 600;
        }

        /// <summary>
        /// 获取状态码描述
        /// </summary>
        /// <returns>状态码描述</returns>
        public string GetStatusDescription()
        {
            return StatusCode.ToString();
        }

        /// <summary>
        /// 设置错误信息
        /// </summary>
        /// <param name="errorMessage">错误信息</param>
        /// <param name="exception">异常对象</param>
        public void SetError(string errorMessage, Exception exception = null)
        {
            IsSuccess = false;
            ErrorMessage = errorMessage;
            Exception = exception;
        }

        /// <summary>
        /// 获取扩展属性值
        /// </summary>
        /// <typeparam name="T">属性值类型</typeparam>
        /// <param name="propertyName">属性名称</param>
        /// <returns>属性值</returns>
        public T GetExtendedProperty<T>(string propertyName)
        {
            if (ExtendedProperties.ContainsKey(propertyName))
            {
                return (T)ExtendedProperties[propertyName];
            }
            return default(T);
        }

        /// <summary>
        /// 设置扩展属性值
        /// </summary>
        /// <param name="propertyName">属性名称</param>
        /// <param name="propertyValue">属性值</param>
        public void SetExtendedProperty(string propertyName, object propertyValue)
        {
            if (ExtendedProperties.ContainsKey(propertyName))
            {
                ExtendedProperties[propertyName] = propertyValue;
            }
            else
            {
                ExtendedProperties.Add(propertyName, propertyValue);
            }
        }

        /// <summary>
        /// 转换为字符串表示
        /// </summary>
        /// <returns>字符串表示</returns>
        public override string ToString()
        {
            return $"[{StatusCodeValue}] {StatusCode} - {(IsSuccess ? "成功" : "失败")} - 耗时: {ElapsedMilliseconds}ms";
        }
    }
}
