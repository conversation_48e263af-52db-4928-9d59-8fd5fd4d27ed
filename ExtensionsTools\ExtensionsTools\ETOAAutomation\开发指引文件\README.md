# 🚀 ETOAAutomation - OA系统自动化辅助库

## 📋 项目简介

ETOAAutomation是一个专为OA系统自动化操作设计的C#辅助库，基于CefSharp和Flurl.Http技术栈，提供完整的登录认证、API交互、文件上传、会话管理和网页自动化操作功能。

## 🎯 核心特性

- **🔐 智能登录认证** - 基于CefSharp的自动化登录，支持验证码处理
- **🌐 强大API交互** - 基于Flurl.Http的现代化HTTP客户端
- **📁 文件上传支持** - 支持单文件和批量文件上传，带进度监控
- **🔄 会话状态管理** - 自动维护登录状态，支持断线重连
- **🎮 网页自动化操作** - 支持DOM操作和坐标操作两种模式
- **💾 本地数据存储** - 加密存储认证信息，支持多用户管理
- **🔧 ExtensionsTools集成** - 充分利用现有的配置、日志、异常处理模块

## 📁 项目结构

```
ETOAAutomation/
├── 📄 README.md                        # 项目说明文档
├── 📄 开发规划.md                       # 详细开发规划
├── 📄 技术手册-Flurl.Http和CefSharp.md   # 技术参考手册
├── 📄 环境配置和依赖.md                  # 环境配置指南
│
├── 🔧 核心模块/
│   ├── ETOAClient.cs                   # 主要OA客户端类
│   ├── ETOALoginBrowser.cs             # 登录认证浏览器
│   ├── ETOAApiClient.cs                # API交互客户端
│   ├── ETOASessionManager.cs           # 会话状态管理器
│   ├── ETOAFileUploader.cs             # 文件上传处理器
│   └── ETOASimulationBrowser.cs        # 模拟操作浏览器
│
├── 📊 数据模型/
│   ├── Models/
│   │   ├── ETOALoginInfo.cs            # 登录信息模型
│   │   ├── ETOAApiRequest.cs           # API请求模型
│   │   ├── ETOAApiResponse.cs          # API响应模型
│   │   ├── ETOAUploadResult.cs         # 上传结果模型
│   │   └── ETOASessionData.cs          # 会话数据模型
│
├── 🛠️ 辅助工具/
│   ├── Helpers/
│   │   ├── ETOAJsonHelper.cs           # JSON处理辅助
│   │   ├── ETOACookieHelper.cs         # Cookie处理辅助
│   │   ├── ETOAConfigHelper.cs         # 配置管理辅助
│   │   └── ETOAStorageHelper.cs        # 本地存储辅助
│
├── 💾 存储管理/
│   ├── Storage/
│   │   ├── ETOAAuthStorage.cs          # 认证信息存储
│   │   └── ETOASessionStorage.cs       # 会话状态存储
│
└── 📚 使用示例/
    ├── Examples/
    │   ├── ETOABasicExample.cs         # 基础使用示例
    │   └── ETOAAdvancedExample.cs      # 高级功能示例
```

## 🚀 快速开始

### 1. 环境准备
```csharp
// 安装必要的NuGet包
Install-Package CefSharp.WinForms -Version 126.2.180
Install-Package Flurl.Http -Version 4.0.2
Install-Package Newtonsoft.Json -Version 13.0.3
```

### 2. 基础使用示例
```csharp
// 创建OA客户端
var oaClient = new ETOAClient("https://oa.company.com");

// 配置登录信息
await oaClient.LoginAsync("username", "password");

// 调用API
var result = await oaClient.GetApiDataAsync<UserInfo>("/api/user/info");

// 上传文件
var uploadResult = await oaClient.UploadFileAsync("/api/upload", 
    filePath, new { category = "document", title = "测试文档" });
```

### 3. 高级自动化操作
```csharp
// 创建模拟操作浏览器
var browser = new ETOASimulationBrowser("https://oa.company.com");

// DOM操作方式
await browser.FillTextAsync("#username", "用户名");
await browser.ClickElementAsync("#loginBtn");

// 坐标操作方式
await browser.ClickAtAsync(100, 200);
await browser.SendKeysAsync("Hello World");

// 等待页面加载完成
await browser.WaitForElementAsync(".dashboard", 10000);
```

## 🔧 配置说明

### 基础配置
```ini
[OA]
BaseUrl=https://oa.company.com
LoginPath=/login
ApiPath=/api

[Network]
TimeoutSeconds=30
MaxRetries=3
AutoRetry=true

[Browser]
UserAgent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36
EnableJavaScript=true
EnableCookies=true

[Storage]
EncryptionEnabled=true
SessionTimeout=3600
AutoCleanup=true
```

### 日志配置
```csharp
// 使用ExtensionsTools的日志管理
ETLogManager.Info("OA客户端初始化完成");
ETLogManager.Error("API请求失败", exception);
```

## 📖 详细文档

- **[开发规划.md](开发规划.md)** - 完整的开发计划和里程碑
- **[技术手册-Flurl.Http和CefSharp.md](技术手册-Flurl.Http和CefSharp.md)** - 技术实现参考
- **[环境配置和依赖.md](环境配置和依赖.md)** - 环境搭建和部署指南

## 🎯 主要功能模块

### 🔐 ETOALoginBrowser - 登录认证
- 自动化OA系统登录流程
- 支持验证码识别和处理
- 完整的认证信息提取
- 多账户管理支持

### 🌐 ETOAApiClient - API交互
- 基于Flurl.Http的现代化HTTP客户端
- 自动JSON序列化/反序列化
- 智能错误处理和重试机制
- Cookie和认证头自动管理

### 📁 ETOAFileUploader - 文件上传
- 支持单文件和批量上传
- 实时进度监控和回调
- 表单数据同步发送
- 断点续传支持

### 🔄 ETOASessionManager - 会话管理
- 登录状态实时监控
- 定期心跳维护机制
- 自动重新登录功能
- 会话数据持久化

### 🎮 ETOASimulationBrowser - 自动化操作
- 内嵌Chromium浏览器
- DOM操作和坐标操作双模式
- 丰富的事件系统
- 操作录制和回放功能

### 💾 本地存储系统
- 加密存储认证信息
- 多用户会话管理
- 自动清理过期数据
- 安全的密钥管理

## 🛡️ 安全特性

- **数据加密** - 本地存储的敏感信息采用加密保护
- **会话安全** - 自动检测和处理会话过期
- **错误处理** - 完善的异常处理和错误恢复机制
- **日志记录** - 详细的操作日志，便于问题排查

## 🔧 扩展性设计

- **插件架构** - 支持自定义扩展和插件开发
- **配置驱动** - 通过配置文件灵活控制行为
- **事件机制** - 丰富的事件回调和通知系统
- **接口抽象** - 清晰的接口定义，便于功能扩展

## 📈 性能优化

- **连接复用** - HTTP连接池管理，提高请求效率
- **智能缓存** - 合理的数据缓存策略
- **资源管理** - 自动资源清理，防止内存泄漏
- **并发控制** - 合理的并发请求控制

## 🚨 注意事项

### 兼容性说明
- **不影响现有模块** - 本项目作为独立模块，不会修改现有的ETLoginWebBrowser等模块
- **ExtensionsTools集成** - 充分利用现有的ETIniFile、ETLogManager、ETException等功能
- **向后兼容** - 保持与现有代码的完全兼容性

### 系统要求
- **.NET Framework 4.7.2+** 或 **.NET Core 3.1+**
- **Windows 10/11** (64位)
- **Visual C++ Redistributable** 2019或更新版本
- **至少2GB可用内存**

### 部署要求
- 确保所有CefSharp运行时文件正确部署
- 配置正确的应用程序权限
- 网络连接能够访问目标OA系统

## 🤝 贡献指南

1. **代码规范** - 遵循现有的代码风格和命名约定
2. **文档更新** - 新功能需要同步更新相关文档
3. **测试覆盖** - 确保新功能有适当的测试覆盖
4. **向后兼容** - 保持与现有代码的兼容性

## 📞 技术支持

如果在使用过程中遇到问题，请：

1. 查看相关文档和示例代码
2. 检查日志文件中的错误信息
3. 确认环境配置是否正确
4. 联系技术支持团队

## 📄 许可证

本项目遵循与ExtensionsTools相同的许可证协议。

---

**ETOAAutomation** - 让OA系统自动化变得简单高效！ 🚀
